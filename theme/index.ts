// import antdTheme from './antdTheme';
export default {
  width: {
    24: '24px',
    36: '36px',
    28: '28px',
    32: '32px',
    42: '42px',
    45: '45px',
    60: '60px',
    64: '64px',
    73: '73px',
    80: '80px',
    110: '110px',
    120: '120px',
    124: '124px',
    140: '140px',
    156: '156px',
    180: '180px',
    200: '200px',
    450: '450px',
    970: '970px',
  },
  height: {
    22: '22px',
    24: '24px',
    28: '28px',
    32: '32px',
    36: '36px',
    38: '38px',
    44: '44px',
    45: '45px',
    40: '40px',
    54: '54px',
    74: '74px',
    1000: '1000px',
  },
  maxHeight: {
    '60vh': '60vh',
  },
  minHeight: {
    84: '84px',
  },
  margin: {
    2: '2px',
    4: '4px',
    5: '5px',
    8: '8px',
    9: '9px',
    10: '10px',
    12: '12px',
    16: '16px',
    18: '18px',
    24: '24px',
    30: '30px',
    32: '32px',
    35: '35px',
  },
  padding: {
    2: '2px',
    5: '5px',
    8: '8px',
    9: '9px',
    10: '10px',
    15: '15px',
    16: '16px',
    18: '18px',
    27: '27px',
    21: '21px',
    24: '24px',
    32: '32px',
    40: '40px',
    42: '42px',
    54: '54px',
  },
  inset: {
    100: '100px',
  },
  borderRadius: {
    4: '4px',
    6: '6px',
    8: '8px',
    10: '10px',
  },
  borderWidth: {
    1: '1px',
  },
  fontSize: {
    56: ['56px', '56px'],
    40: ['40px', '48px'],
    34: ['34px', '32px'],
    28: ['28px', '28px'],
    24: ['24px', '34px'],
    20: ['20px', '28px'],
    16: ['16px', '22px'],
    '16-40': ['16px', '40px'],
    14: ['14px', '19px'],
    12: ['12px', '16px'],
    10: ['10px', '16px'],
  },
  colors: {
    neutral: {
      13: '#000000',
      0: '#000000',
      10: '#1A1A1A',
      20: '#333333',
      25: '#404040',
      30: '#4D4D4D',
      35: '#595959',
      40: '#666666',
      45: '#737373',
      50: '#808080',
      55: '#8C8C8C',
      60: '#999999',
      65: '#A6A6A6',
      70: '#B2B2B2',
      75: '#BFBFBF',
      80: '#CCCCCC',
      85: '#D9D9D9',
      90: '#E5E5E5',
      93: '#EDEDED',
      97: '#F7F7F7',
      100: '#FFFFFF',
    },
    link: {
      100: '#F0F6FF',
      200: '#d6e4ff',
      300: '#adc7ff',
      600: '#3059ed',
      700: '#3059EB',
    },
    success: {
      100: '#E6F8DE',
      200: '#C8F5B2',
      300: '#A3E982',
      600: '#00B42A',
    },
    warning: {
      100: '#FFF5E7',
      200: '#FFE5BD',
      300: '#FFC977',
      600: '#FA9600',
    },
    error: {
      100: '#FFF0F0',
      200: '#FFD8D8',
      300: '#FF9999',
      600: '#E82424',
    },
    primary: {
      //主题色彩
      100: '#12FFD4',
      200: '#0FFFD4',
      700: '#1A56DB',
      // colorBgContainer: antdTheme.colorBgContainer,
      warn1: '#F43434',
      warn2: '#FF8A00',
      warn3: '#FFD233',
    },
    orange: {
      300: '#FDBA8C',
    },
    secondary: {
      100: '#FCFCFC',
      200: '#fff0b5',
      300: '#ffc782',
      400: '#ffd663',
      500: '#B8B8B8',
      600: '#f5a911',
      700: '#cf8404',
      800: '#a86500',
      900: '#824a00',
      1000: '#5c3100',
    },
    text: {
      '007DFA': '#007DFA',
      '6B7280': '#6B7280',

      FFD90F: '#FFD90F',
      '0FFFD4': '#0FFFD4',
      E5E7EB: '#E5E7EB',
      F9FAFB: '#F9FAFB',
      '1C64F2': '#1C64F2',
      '69C770': '#69C770',
      D9D9D9: '#D9D9D9',
      FFFFFF: '#FFFFFF',
      DEDEDE: '#DEDEDE',
      383874: '#383874',
      '77757F': '#77757F',
      '34303E': '#34303E',
      '4880F7': '#4880F7',
      F6F9FF: '#F6F9FF',
      E8F3FF: ' #E8F3FF',
      '165DFF': '#165DFF',
    },
  },

  fontFamily: {
    'font*': ['SimSun', 'sans-serif'],
  },
  zIndex: {
    999: '999',
    99: '99',
  },
  cursor: {
    grab: 'grab',
    grabbing: 'grabbing',
  },
};
