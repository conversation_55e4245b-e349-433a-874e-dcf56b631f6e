{"Tsx Temp Fetch": {"prefix": "tsxt", "body": ["import { useEffect, useState } from 'react';", "import api from '@/api';", "import './index.less';", "", "const ${1:Airpots} = () => {", "  const [data, setData] = useState();", "", "  const fetchData = () => {", "    api.getAccessConfirmationUsingGet().then(res => {", "      const {} = res.data;", "      setData();", "    });", "  };", "", "  useEffect(() => {", "    fetchData();", "  }, []);", "", "  return <div></div>;", "};", "", "export default ${1:Airpots};"], "description": "Tsx Temp Fetch"}}