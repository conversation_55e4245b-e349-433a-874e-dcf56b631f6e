# 生产过程治理平台

## 开发

### 常规命令

```shell
# 安装依赖
$ pnpm install

# 启动
$ pnpm start

# 构建打包
$ pnpm build

#自动导入接口
$ pnpm swaggerUi
```

### 项目结构

```
.
├── README.md
├── package-lock.json
├── package.json
├── .env.development
├── .env.production
├── .env.test
├── public
├── templates 接口模版
├── vite.config.ts 项目vite配置
├── index.html
├── .vscode
│   ├── settings.json
│   └── tsx.code-snippets
├── theme
│   ├── antdTheme.ts
│   └── index.ts tailwind的自定义配置
├── src
│   ├── App.css
│   ├── App.tsx
│   ├── layout
│   ├── store 数据store
│   ├── pages
│   ├── index.css
│   ├── index.tsx
│   ├── config
│       ├── routes.tsx
│   ├── api   接口自动化生成
│       ├── axiosConfig.ts  请求接口配置
│       ├── servies
│       └── index.ts
│   └── utils
└── tsconfig.json

```

### 项目风格介绍

1. 整体样式请使用 tailwind，所以自定义 class 请慎重且注意环境污染问题

2. 本项目内置了 store、订阅发布类、常规函数等，请先了解本项目结构再进行开发

3. 请求内置了配置 react-query

4. 如果使用 antd 的话，全局 antd 的主题[token 配置](https://ant.design/components/config-provider-cn)可以放在 theme/antdTheme 中，然后再 app.tsx 测注册全局即可

5. 请整体工程使用 pnpm

6. 全局状态管理请挂载在 store/\*中，可参考现有例子

7. 整体较高模版请维护在.vscode/\*

8. 默认配置多环境 env 文件，在打包命令中可以使用--mode 来指定相关 env 文件

9. 项目组件风格请保持一致，components/index.tsx 或者 components.tsx 请勿混用

10. 项目接口文件 src/api/\*,除了 axiosConfig 需要自定义之外，其他都使用 pnpm swaggerUi 之后进行操作，初次使用可以参考现有 api，但是导入正确 api 之后请删除原本 demo 中的 api

11. 接口名称生成规则是按照swaggerUi的json数据的中operationId为一级命名，次级方案是根据url路径自动拼接组成接口方法名，！！！所以请根据你自己的需求进行采纳。默认是走operationId，如果不需要，则在templates/router-name.ejs中删除`if (operationId) return _.camelCase(operationId);`的逻辑语法即可。

12. .env 文件中的 VITE_BASE_URL 为页面路由 base

13. 项目中使用的接口数据类型定义请使用 api 自动生成的类型，鼠标悬浮即可获取类型引用

14. 如果你觉得你写不好类型定义并且很想用 any 的时候，请慎重

15. 项目路由除了 login 等不需要 layout 都请维护在 layout 的 children 中

16. 项目提交规范严格按照[type]: xxxxx 等格式严格校验

17. 项目提交 eslint 以及 ts 的 error 都需处理，代码提交 commit 中有函数校验，校验不过 git 将驳回

18. 使用中如有疑问、改进建议、好的 idea 请提交 issues 或者 直接联系 <EMAIL>

19.测试环境jenkins地址：http://192.168.17.81:8080
