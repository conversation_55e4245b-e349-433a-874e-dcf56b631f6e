import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import path from 'path';
// import { compression } from 'vite-plugin-compression2';

export default ({ mode }) => {
  const env = loadEnv(mode, process.cwd());

  return defineConfig({
    base: env.VITE_BASE_URL,

    //gz压缩优化，目前仅压缩js，css等文件，需要nginx开启gz压缩
    // plugins: [react(), compression({ include: [/\.(js)$/, /\.(css)$/] })],

    plugins: [react()],
    server: {
      host: '0.0.0.0',
      proxy: {
        '/pmt': 'http://ppguat.iprd.sw/',
        // '/pmt': 'http://ppgtest.iprd.sw/',
        // '/pmt': 'http://ppg.iprd.sw/',
        // '/pmt': 'http://**************:8082/',
      },
    },
    // esbuild: {
    //   drop:
    //     process.env.NODE_ENV === 'development' ? [] : ['console', 'debugger'],
    // },
    build: {
      //拆包优化,根据自己的需求进行拆分
      rollupOptions: {
        output: {
          manualChunks: {
            'react-vendor': ['react', 'react-dom'],
          },
        },
        input: {
          index: path.resolve(__dirname, 'index.html'),
          screen: path.resolve(__dirname, 'screen.html'),
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
  });
};
