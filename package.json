{"name": "react-app", "private": true, "version": "0.0.1", "scripts": {"dev": "vite", "build": "vite build --mode production", "build:test": "vite build --mode test", "preview": "vite preview", "lint": "eslint --ext .js,.jsx,.ts,.tsx ./src", "lint:fix": "eslint --fix --ext .js,.jsx,.ts,.tsx ./src", "format": "prettier --write 'src/**/**/*.{js,jsx,ts,tsx,css,md,json}' --config ./prettier.config.js", "cz": "cz", "prepare": "husky install", "swaggerUi": "node swaggerUi", "preinstall": "npx only-allow pnpm", "eslint": "eslint src --fix --ext .js,.jsx,.ts,.tsx", "stylelint": "stylelint **/*.{css,less,scss} --fix", "prettier": "prettier -c --write \"**/*\"", "commitlint": "commitlint --config .commitlintrc.js -e -V"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": "eslint --config .eslintrc.js", "*.{css,less,scss}": "stylelint --config .stylelintrc.js", "*.{js,jsx,ts,tsx,less,md,json}": "prettier --write"}, "config": {}, "dependencies": {"@ant-design/icons": "^5.4.0", "@ant-design/pro-components": "2.8.6", "@hbwow/components": "2.18.1", "@hbwow/hooks": "2.7.3", "@hbwow/utils": "^2.5.3", "@hbwow/validate-antd": "^1.1.0", "@tanstack/react-query": "5.66.9", "@tanstack/react-query-devtools": "^5.66.9", "antd": "5.24.1", "axios": "1.3.4", "classnames": "^2.5.1", "dayjs": "1.11.8", "echarts": "^5.5.1", "lodash-es": "^4.17.21", "react": "18.2.0", "react-dom": "18.2.0", "react-router-dom": "6.8.1", "zustand": "5.0.5"}, "devDependencies": {"@commitlint/cli": "17.0.3", "@commitlint/config-conventional": "17.0.3", "@hbwow/lints": "^2.1.1", "@types/lodash-es": "^4.17.12", "@types/react": "18.0.27", "@types/react-dom": "18.0.10", "@typescript-eslint/parser": "5.37.0", "@vitejs/plugin-react-swc": "3.3.0", "autoprefixer": "10.4.13", "commitizen": "4.2.5", "commitlint-config-cz": "0.13.3", "cssnano": "6.0.1", "cz-customizable": "7.0.0", "eslint": "8.52.0", "husky": "8.0.0", "less": "4.1.3", "lint-staged": "15.5.0", "postcss": "8.4.21", "postcss-import": "15.1.0", "prettier": "3.5.3", "stylelint": "15.11.0", "swagger-typescript-api": "12.0.3", "tailwindcss": "3.3.2", "typescript": "5.1.3", "vite": "4.5.10", "vite-plugin-compression2": "0.11.0"}}