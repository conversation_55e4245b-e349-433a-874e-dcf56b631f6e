/**
 *获取url特定的参数
 *
 * @export
 * @param {*} name
 * @returns
 */
export function getQueryVariable(name: string) {
  const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
  const r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return '';
}

// 存储数据到 localStorage
export const saveDataToLocalStorage = (key: string, data: any): void => {
  try {
    const serializedData =
      typeof data === 'string' ? data : JSON.stringify(data);
    localStorage.setItem(key, serializedData);
  } catch (error) {
    console.error(`保存数据到 localStorage 时出现错误: ${error}`);
  }
};

// 从 localStorage 中获取数据
export const getDataFromLocalStorage = (key: string): any | null => {
  try {
    const serializedData = localStorage.getItem(key);
    if (serializedData === null) {
      console.log(`找不到键名为 ${key} 的数据`);
      return null;
    }
    try {
      return JSON.parse(serializedData);
    } catch {
      // 如果解析 JSON 失败，说明存储的是普通字符串
      return serializedData;
    }
  } catch (error) {
    console.error(`从 localStorage 获取数据时出现错误: ${error}`);
    return null;
  }
};
