const LOGIN_URL = '/pmt/oauth/token';
const USER = {
  username: 'pmt_admin',
  password: 'l1IeAtpHTsDb0AN/UIDv7Q==',
  grant_type: 'password',
};

let _isShow = false;

const bodyAddMaskDom = () => {
  const maskDom = document.createElement('div');
  Object.assign(maskDom.style, {
    position: 'fixed',
    top: '0',
    left: '0',
    width: '100vw',
    height: '100vh',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: '99999', // 提高层级
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  });

  const loadingDom = document.createElement('div');
  Object.assign(loadingDom.style, {
    color: '#fff',
    fontSize: '20px',
    fontWeight: 'bold',
    textAlign: 'center',
  });

  // 添加一个简单的加载动画
  const dotAnimation = () => {
    const dots = ['', '.', '..', '...'];
    let index = 0;
    return setInterval(() => {
      loadingDom.textContent = '登录中' + dots[index];
      index = (index + 1) % dots.length;
    }, 500);
  };

  // 将 loading 添加到遮罩层中
  maskDom.appendChild(loadingDom);
  // 将遮罩层添加到 body 中
  document.body.appendChild(maskDom);

  // 启动动画
  const animationTimer = dotAnimation();

  // 返回清理函数
  return () => {
    clearInterval(animationTimer);
    document.body.removeChild(maskDom);
  };
};

const devEnvOneClickLogin = () => {
  if (import.meta.env.DEV && !_isShow) {
    if (window.confirm('🫵🫵🫵开发环境一键登录( devEnvOneClickLogin )🫵🫵🫵')) {
      _isShow = true;
      const removeMask = bodyAddMaskDom();

      const params = new URLSearchParams(USER);
      fetch(`${LOGIN_URL}?${params.toString()}`, {
        method: 'GET',
        headers: {
          Authorization: 'Basic cGFzc3dvcmRfYXV0aF9tb2RlOjEyMzQ1Ng==',
        },
      })
        .then(response => {
          return response.json();
        })
        .then(data => {
          const { access_token } = data;
          localStorage.setItem('authorization', access_token);

          _isShow = false;
          window.location.href = '/';
        })
        .catch(error => {
          _isShow = false;
          removeMask(); // 错误时也要移除遮罩层
          // eslint-disable-next-line no-console
          console.error('登录失败:', error);
        });
    }
  }
};

export default devEnvOneClickLogin;
