// import { commonApis, getBaseResult } from '@/api';
// import { create } from 'zustand';

// type DictStore = {
//   allDicts: Record<string, any>;
//   initAllDicts: () => void;
//   getDictValue: (type: string, label: string) => any;
//   getDictLabel: (type: string, value: string) => any;
// };

// export const custDictYesNo = { 0: '否', 1: '是' };

// export const useDictStore = create<DictStore>((set, get) => ({
//   allDicts: {},

//   initAllDicts: async () => {
//     const r: any =
//       (await getBaseResult(
//         commonApis.getAllUsingGet1(),
//         '获取基础数据失败，请检查网络！',
//       )) ?? {};
//     Object.keys(r).forEach(key => {
//       const map: any = {};
//       r[key].map((v: any) => (map[v.dictValue] = v.dictLabel));
//       r[key] = map;
//     });
//     set(() => ({ allDicts: r }));
//   },

//   getDictValue: (type: string, label: string) =>
//     get().allDicts[type]?.first((d: any) => (d.dictLabel = label)),

//   getDictLabel: (type: string, value: string) =>
//     get().allDicts[type]?.first((d: any) => (d.dictValue = value)),
// }));
