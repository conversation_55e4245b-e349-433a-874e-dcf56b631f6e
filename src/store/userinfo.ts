// import { getBaseResult, ucApis } from '@/api';
// import { UserCenterTenantVO } from '@/api/servies/ucApi';
// import { create } from 'zustand';

// type UserInfoStore = {
//   userInfo: UserCenterTenantVO;
//   initUserInfo: () => void;
// };

// export const useUserInfoStore = create<UserInfoStore>(set => ({
//   userInfo: {},
//   initUserInfo: async () => {
//     const r: any = await getBaseResult(
//       ucApis.getUserInfoUsingGet(),
//       '获取用户数据失败，请检查网络！',
//     );
//     set(() => ({ userInfo: r }));
//   },
// }));
