import { useRef } from 'react';
import { ProTable as AntdProTable, FormInstance, ProTableProps } from '@ant-design/pro-components';

interface IProps extends ProTableProps {
  pageRequest?: Promise<any>;
  storeKey?: string;
}

const ProTable = (props: IProps) => {
  const { formRef, storeKey = '', pageRequest, ...rest } = props;

  const [tableParams, updateTableParams] = useStore(
    useShallow((store) => [store.tableParams[storeKey], store.updateTableParams]),
  );

  const _formRef = useRef<FormInstance>();
  const curFormRef = formRef || _formRef;

  const defaultProps = {
    formRef: curFormRef,
    //  debounceTime:0,
    scroll: { x: 'max-content' },
    editable: {
      type: 'multiple',
    },
    rowKey: 'id',
    options: false,
    form: {
      initialValues: storeKey ? tableParams : undefined,
    },
    onReset: () => {
      // console.log({ formRef, _formRef, curFormRef }, '-----');

      const resetData = Object.keys(curFormRef.current?.getFieldsValue()).reduce(
        (acc, cur) => {
          acc[cur] = undefined;
          return acc;
        },
        {} as Record<string, any>,
      );

      curFormRef.current?.setFieldsValue(resetData);
      curFormRef.current?.submit();

      return;
    },
    request: async (params) => {
      updateTableParams(storeKey, params);

      const r = await pageRequest?.({
        ...params,
        pageNumber: params.current,
        pageSize: params.pageSize,
      });

      return {
        data: r?.data?.data,
        success: !!r?.data?.data,
        total: r?.data?.totalRecords,
      };
    },
    pagination: {
      defaultPageSize: 20,
      // showQuickJumper: true,
      showSizeChanger: true,
    },
    dateFormatter: 'yyyy-MM-dd',
    search: {
      labelWidth: 'auto',
      defaultCollapsed: false,
    },
  };

  const proTableProps = mergeWith(defaultProps, { ...rest });

  return <AntdProTable />;
};

export default ProTable;
