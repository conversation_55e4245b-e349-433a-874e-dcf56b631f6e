# 解决 React Key 警告指南

## 问题描述

当使用 ProTable 组件时，可能会遇到以下警告：
```
Warning: Each child in a list should have a unique "key" prop.
```

## 常见原因和解决方案

### 1. toolBarRender 中缺少 key

**❌ 错误示例：**
```tsx
<ProTable
  toolBarRender={() => [
    <Button type="primary">添加</Button>,  // 缺少 key
    <Button>导出</Button>,                 // 缺少 key
  ]}
/>
```

**✅ 正确示例：**
```tsx
<ProTable
  toolBarRender={() => [
    <Button key="add" type="primary">添加</Button>,
    <Button key="export">导出</Button>,
  ]}
/>
```

### 2. 操作列中缺少 key

**❌ 错误示例：**
```tsx
{
  title: '操作',
  valueType: 'option',
  render: (_, record) => [
    <Button size="small">编辑</Button>,    // 缺少 key
    <Button size="small">删除</Button>,    // 缺少 key
  ],
}
```

**✅ 正确示例：**
```tsx
{
  title: '操作',
  valueType: 'option',
  render: (_, record) => [
    <Button key="edit" size="small">编辑</Button>,
    <Button key="delete" size="small">删除</Button>,
  ],
}
```

### 3. 自定义渲染中缺少 key

**❌ 错误示例：**
```tsx
{
  title: '标签',
  render: (_, record) => 
    record.tags.map(tag => <Tag>{tag}</Tag>)  // 缺少 key
}
```

**✅ 正确示例：**
```tsx
{
  title: '标签',
  render: (_, record) => 
    record.tags.map((tag, index) => <Tag key={`${record.id}-${tag}-${index}`}>{tag}</Tag>)
}
```

### 4. 数据源缺少唯一标识

**问题：** 如果数据源中没有 `id` 字段，ProTable 可能无法正确生成 rowKey。

**解决方案：** 
1. 确保数据源包含唯一的 `id` 字段
2. 或者自定义 `rowKey` 属性

```tsx
<ProTable
  rowKey="customId"  // 使用其他字段作为 key
  // 或者使用函数
  rowKey={(record) => `${record.name}-${record.index}`}
/>
```

## ProTable 组件的改进

我们的 ProTable 组件已经内置了智能的 rowKey 处理：

```tsx
rowKey: (record: DataType) => {
  const recordWithId = record as Record<string, unknown>;
  return String(recordWithId?.id || recordWithId?.key || JSON.stringify(record));
}
```

这确保了即使数据没有 `id` 字段，也能生成唯一的 key。

## 最佳实践

1. **始终为数组元素添加 key**：在 `toolBarRender`、操作列等返回数组的地方
2. **使用有意义的 key**：避免使用数组索引，使用业务相关的唯一标识
3. **确保数据源的唯一性**：每条数据都应该有唯一标识
4. **检查嵌套组件**：确保所有子组件也正确设置了 key

## 调试技巧

1. 打开浏览器开发者工具的 Console
2. 查看具体的警告信息，定位到具体的组件
3. 检查相关的渲染函数是否正确设置了 key
4. 使用 React Developer Tools 查看组件树结构
