import { useRef } from 'react';
import { ActionType, ProColumns } from '@ant-design/pro-components';
import { Button } from 'antd';
import { PlusOutlined, DownloadOutlined } from '@ant-design/icons';
import ProTable from './ProTable';

// 示例数据类型
interface UserData {
  id: string;
  name: string;
  age: number;
  email: string;
  createTime: string;
}

const ProTableDemo = () => {
  const actionRef = useRef<ActionType>();

  // 列配置
  const columns: ProColumns<UserData>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
      valueType: 'digit',
      hideInSearch: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
  ];

  // 模拟 API 请求
  const fetchUserData = async (params: Record<string, unknown>) => {
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟数据
    const mockData: UserData[] = Array.from({ length: 50 }, (_, index) => ({
      id: `${index + 1}`,
      name: `用户${index + 1}`,
      age: 20 + (index % 30),
      email: `user${index + 1}@example.com`,
      createTime: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
    }));

    // 模拟分页
    const { pageNumber = 1, pageSize = 20 } = params;
    const start = ((pageNumber as number) - 1) * (pageSize as number);
    const end = start + (pageSize as number);
    const data = mockData.slice(start, end);

    return {
      data: {
        data,
        totalRecords: mockData.length,
      },
    };
  };

  return (
    <div style={{ padding: 24 }}>
      <ProTable<UserData>
        columns={columns}
        actionRef={actionRef}
        pageRequest={fetchUserData}
        storeKey="user-table-demo"
        headerTitle="用户列表示例"
        toolBarRender={() => [
          // 注意：为每个按钮添加唯一的 key 属性
          <Button
            key="add"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              console.log('添加用户');
            }}
          >
            添加用户
          </Button>,
          <Button
            key="export"
            icon={<DownloadOutlined />}
            onClick={() => {
              console.log('导出数据');
            }}
          >
            导出
          </Button>,
        ]}
      />
    </div>
  );
};

export default ProTableDemo;
