import { create } from 'zustand';

// 表格存储接口
export interface IStore {
  tableParams: { [key: string]: Record<string, unknown> };
  updateTableParams: (storeKey: string | undefined, params: Record<string, unknown>) => void;
}

const useStore = create<IStore>()((set) => ({
  tableParams: {},
  updateTableParams: (storeKey, params) => {
    if (!storeKey) {
      return;
    }

    return set((store) => ({
      tableParams: {
        ...store.tableParams,
        [storeKey]: params,
      },
    }));
  },
}));

export default useStore;
