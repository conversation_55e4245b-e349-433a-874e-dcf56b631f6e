// import { Button, ButtonProps } from 'antd';
// import { ExclamationCircleOutlined } from '@ant-design/icons';

// import cx from 'classnames';

// interface IUsageTipsProps {
//   buttonProps: ButtonProps;
// }

// const UsageTips = ({ buttonProps = {} }: IUsageTipsProps) => {
//   const { className: btnClassName, ...btnRest } = buttonProps;

//   return (
//     <Button
//       type="text"
//       icon={<ExclamationCircleOutlined />}
//       className={cx('', btnClassName)}
//       {...btnRest}
//     />
//   );
// };

// export default UsageTips;

const UsageTips = () => {
  return <></>;
};

export default UsageTips;
