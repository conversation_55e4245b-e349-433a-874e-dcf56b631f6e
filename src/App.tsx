import Router from '@/config/router';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { saveTokenFormUrl } from './api/axiosConfig';
// import { scan } from 'react-scan';
import { ConfigProvider as ValidateConfigProvider } from '@hbwow/validate-antd';

dayjs.locale('zh-cn');

// scan({
//   enabled: true,
// });

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false, // 查询重试
      refetchOnWindowFocus: false, // 窗口聚焦重新获取
      staleTime: 0, // 数据新鲜期
      gcTime: 0, // 缓存垃圾回收时间
      refetchOnMount: true,
    },
  },
});

const rulesMap = {
  isEmptyIsPhone: (value: string) => {
    if (!value) {
      return '不能为空！';
    }
    if (!new RegExp(/^((13[0-9])|(14[0-9])|(15[0-9])|(17[0-9])|(18[0-9]))\d{8}$/).test(value)) {
      return '请输入正确的手机号码！';
    }
    return '';
  },
  isNullOrUndefined: (value: any) => {
    if (value === null || value === undefined) {
      return '不能为空！';
    }
    return '';
  },
};

function App() {
  saveTokenFormUrl();

  return (
    <QueryClientProvider client={queryClient}>
      <ValidateConfigProvider rulesMap={rulesMap}>
        <div>
          <BrowserRouter basename={import.meta.env.VITE_BASE_URL}>
            <Router />
          </BrowserRouter>
        </div>
      </ValidateConfigProvider>

      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}

export default App;
