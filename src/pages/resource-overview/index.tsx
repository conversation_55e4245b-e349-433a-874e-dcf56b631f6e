import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { useRef } from 'react';
import './index.less';
import { plmbaseApis } from '@/api';
import { CommonProTable, CustomUpload } from '@/common/common-view';
import { useDictStore } from '@/store/dictionary';
import { useNavigate } from 'react-router-dom';

const ResourceOverview = () => {
  const actionRef = useRef<ActionType>();
  const { allDicts } = useDictStore();
  const navi = useNavigate();

  const columns: ProColumns[] = [
    {
      title: '工号',
      dataIndex: 'jobNumber',
    },
    {
      title: '姓名',
      dataIndex: 'realName',
    },
    {
      title: '交付组',
      dataIndex: 'teamId',
      valueEnum: () => allDicts['plm_deliver_teams'],
    },
    {
      title: '岗位',
      dataIndex: 'jobId',
      valueEnum: () => allDicts['plm_jobs'],
    },
    {
      title: '状态',
      dataIndex: 'staffState',
      valueEnum: () => allDicts['uc_employee_status'],
    },
    {
      title: '入职日期',
      dataIndex: 'entryDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '离职日期',
      dataIndex: 'departDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      render: () => [
        <a key="1" onClick={() => navi('/resourceManager/statusDuty')}>
          在岗状态
        </a>,
        <a key="2" onClick={() => navi('/resourceManager/statusLoad')}>
          月度负载
        </a>,
        <a key="3" onClick={() => navi('/resourceManager/statusUsage')}>
          月度投入
        </a>,
      ],
    },
  ];

  return (
    <>
      <CommonProTable
        columns={columns}
        actionRef={actionRef}
        pageRequest={(para: any) => plmbaseApis.pageUsingPost10(para)}
        headerTitle={null ?? '资源总览'}
        toolBarRender={() => [
          <CustomUpload
            btnName="导入资源总览"
            url="/pmt/staff/res/importData"
            callback={() => actionRef.current?.reload()}
          />,
          <CustomUpload
            btnName="导入在岗状态"
            url="/pmt/staff/ws/importData"
            callback={() => actionRef.current?.reload()}
          />,
          <CustomUpload
            btnName="导入月度负载"
            url="/pmt/staff/wl/importData"
            data={{ dt: 1 }}
            callback={() => actionRef.current?.reload()}
          />,
          <CustomUpload
            btnName="导入月度投入"
            url="/pmt/staff/wl/importData"
            data={{ dt: 2 }}
            callback={() => actionRef.current?.reload()}
          />,
        ]}
      />
    </>
  );
};

export default ResourceOverview;
