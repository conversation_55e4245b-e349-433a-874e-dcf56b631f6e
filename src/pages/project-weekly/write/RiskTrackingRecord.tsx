import { useEffect, useRef, useState } from 'react';

import { Card, CardProps, Form, Input, Modal, Select, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { CommonProTable } from '@/common/common-view';

import { useGetRules } from '@hbwow/validate-antd';
import { plmApis } from '@/api';
import { useDictMap, useDictOptions, useSystemMenu } from '@/api/querys/common';
import { ActionType } from '@ant-design/pro-components';
import { useMutation, useQuery } from '@tanstack/react-query';
import { AuthButton, CustomSuspense } from '@hbwow/components';

const { TextArea } = Input;

const useAddRisk = () => {
  return useMutation({
    mutationFn: async (params: any) => {
      const { data } = await plmApis.plmRiskSaveCreate(params);
      return data;
    },
  });
};
const useGetDetailById = (id?: string | number) => {
  return useQuery({
    queryKey: ['plmApis.plmRiskGetList', id],
    queryFn: async () => {
      const { data } = await plmApis.plmRiskGetList({ id } as { id: number });

      return data?.data;
    },
    enabled: Boolean(id),
  });
};
const useCloseRisk = () => {
  return useMutation({
    mutationFn: async (params: any) => {
      const { data } = await plmApis.plmRiskCloseCreate(params);
      return data;
    },
  });
};
const useSolveRisk = () => {
  return useMutation({
    mutationFn: async (params: any) => {
      const { data } = await plmApis.plmRiskSolveCreate(params);
      return data;
    },
  });
};

interface IRiskTrackingRecord {
  projectCode: string;
  cardProps?: CardProps;
  allDisabled?: boolean;
}

const RiskTrackingRecord = ({
  projectCode = '',
  cardProps = {},
  allDisabled = false,
}: IRiskTrackingRecord) => {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const { data: dataSystemMenu } = useSystemMenu();

  const { getRules } = useGetRules();
  const { data: dictMap } = useDictMap();
  const { data: dataDictOptions, isFetching: isFetchingDictOptions } = useDictOptions();
  const { mutate: mutateAddRisk, isPending: isPendingAddRisk } = useAddRisk();
  const { mutate: mutateCloseRisk, isPending: isPendingCloseRisk } = useCloseRisk();
  const { mutate: mutateSolveRisk, isPending: isPendingSolveRisk } = useSolveRisk();

  type IModalInfo = {
    type: '' | 'add' | 'edit' | 'solve' | 'close';
    open: boolean;
    id?: number | string;
  };
  const defaultModalInfo: IModalInfo = {
    type: '',
    open: false,
  };
  const [modalInfo, setModalInfo] = useState(defaultModalInfo);

  const { data: dataGetDetailById, isFetching: isFetchingGetDetailById } = useGetDetailById(
    modalInfo?.id,
  );

  const handleCancel = () => {
    form.resetFields();
    setModalInfo({
      ...defaultModalInfo,
    });
  };

  const handleOk = async () => {
    const formData = await form.validateFields();
    const params = {
      projectCode,
      ...formData,
    };

    if (modalInfo.type === 'add') {
      mutateAddRisk(params, {
        onSuccess: () => {
          actionRef.current?.reload();
          handleCancel();
        },
      });
    }

    if (modalInfo.type === 'edit') {
      mutateAddRisk(
        { ...params, id: modalInfo.id },
        {
          onSuccess: () => {
            actionRef.current?.reload();
            handleCancel();
          },
        },
      );
    }

    if (modalInfo.type === 'solve') {
      mutateSolveRisk(
        { description: params.description, id: modalInfo.id },
        {
          onSuccess: () => {
            actionRef.current?.reload();
            handleCancel();
          },
        },
      );
    }

    if (modalInfo.type === 'close') {
      mutateCloseRisk(
        { description: params.description, id: modalInfo.id },
        {
          onSuccess: () => {
            actionRef.current?.reload();
            handleCancel();
          },
        },
      );
    }
  };

  const renderTitle = () => {
    if (modalInfo.type === 'add') {
      return '新增风险记录';
    }
    if (modalInfo.type === 'edit') {
      return '编辑风险记录';
    }
    if (modalInfo.type === 'solve') {
      return '解决风险记录';
    }
    if (modalInfo.type === 'close') {
      return '关闭风险记录';
    }
  };
  const renderOkText = () => {
    if (modalInfo.type === 'solve') {
      return '解决';
    }
    if (modalInfo.type === 'close') {
      return '关闭';
    }

    return undefined;
  };

  useEffect(() => {
    if (dataGetDetailById) {
      form.setFieldsValue(dataGetDetailById);
    }
  }, [dataGetDetailById]);

  return (
    <>
      <Card
        title='风险跟踪记录'
        extra={
          <>
            <AuthButton
              authId='projectWeekly_risk_save'
              authList={dataSystemMenu?.perms}
              disabled={allDisabled}
              icon={<PlusOutlined />}
              onClick={() => {
                form.setFieldsValue({
                  riskState: 0,
                });
                setModalInfo({
                  type: 'add',
                  open: true,
                });
              }}
            >
              新增风险
            </AuthButton>
          </>
        }
        {...cardProps}
      >
        <CommonProTable
          bordered
          search={false}
          actionRef={actionRef}
          params={{
            projectCode,
          }}
          columns={[
            {
              title: '风险名称',
              dataIndex: 'riskName',
            },
            {
              title: '风险类型',
              dataIndex: 'riskType',
              valueEnum: () => dictMap.getLabelByValue['plm_risk_type'],
            },
            {
              title: '风险创建日期',
              dataIndex: 'createdTime',
              valueType: 'date',
              fieldProps: {
                format: 'YYYY-MM-DD HH:mm:ss',
              },
            },
            {
              title: '风险创建人',
              dataIndex: 'createdBy',
            },
            {
              title: '风险关闭时间',
              dataIndex: 'closeDate',
              valueType: 'date',
              fieldProps: {
                format: 'YYYY-MM-DD HH:mm:ss',
              },
            },
            {
              title: '风险状态',
              dataIndex: 'riskState',
              valueEnum: () => dictMap.getLabelByValue['plm_risk_state'],
            },
            {
              title: '风险描述',
              dataIndex: 'description',
            },
            {
              title: '操作',
              render: (_, record) => {
                const { id } = record;

                return (
                  <Space>
                    <AuthButton
                      authId='projectWeekly_risk_edit'
                      authList={dataSystemMenu?.perms}
                      disabled={allDisabled}
                      type='link'
                      onClick={() => {
                        setModalInfo({
                          type: 'edit',
                          open: true,
                          id,
                        });
                      }}
                    >
                      编辑
                    </AuthButton>
                    <AuthButton
                      authId='projectWeekly_risk_solve'
                      authList={dataSystemMenu?.perms}
                      disabled={allDisabled}
                      type='link'
                      onClick={() => {
                        setModalInfo({
                          type: 'solve',
                          open: true,
                          id,
                        });
                      }}
                    >
                      解决
                    </AuthButton>
                    <AuthButton
                      authId='projectWeekly_risk_close'
                      authList={dataSystemMenu?.perms}
                      disabled={allDisabled}
                      type='link'
                      onClick={() => {
                        setModalInfo({
                          type: 'close',
                          open: true,
                          id,
                        });
                      }}
                    >
                      关闭
                    </AuthButton>
                  </Space>
                );
              },
            },
          ]}
          pageRequest={plmApis.plmRiskPageCreate}
        />
      </Card>

      <Modal
        title={renderTitle()}
        okText={renderOkText()}
        open={modalInfo.open}
        onCancel={handleCancel}
        onOk={handleOk}
        confirmLoading={isPendingAddRisk || isPendingCloseRisk || isPendingSolveRisk}
      >
        <CustomSuspense isFetching={isFetchingGetDetailById}>
          <Form layout='vertical' form={form}>
            <Form.Item
              label='风险状态'
              name='riskState'
              rules={[...getRules({ name: 'isNullOrUndefined' })]}
            >
              <Select
                disabled
                options={dataDictOptions['plm_risk_state']}
                loading={isFetchingDictOptions}
              />
            </Form.Item>

            <Form.Item label='风险名称' name='riskName' rules={[...getRules({ name: 'isEmpty' })]}>
              <Input disabled={modalInfo.type === 'solve' || modalInfo.type === 'close'} />
            </Form.Item>

            <Form.Item
              label='风险类型'
              name='riskType'
              rules={[...getRules({ name: 'isNullOrUndefined' })]}
            >
              <Select
                disabled={modalInfo.type === 'solve' || modalInfo.type === 'close'}
                options={dataDictOptions['plm_risk_type']}
                loading={isFetchingDictOptions}
              />
            </Form.Item>

            <Form.Item
              label='风险描述'
              name='description'
              rules={[...getRules({ name: 'isEmpty' })]}
            >
              <TextArea rows={6} />
            </Form.Item>
          </Form>
        </CustomSuspense>
      </Modal>
    </>
  );
};

export default RiskTrackingRecord;
