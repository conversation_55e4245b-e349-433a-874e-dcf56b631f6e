import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from '@tanstack/react-query';

import { PageHeader } from '@ant-design/pro-components';
import { Button, Card, Input, InputNumber, message } from 'antd';
import RiskTrackingRecord from './RiskTrackingRecord';
import { CustomSuspense, RequiredIcon } from '@hbwow/components';
import { CommonProTable } from '@/common/common-view';

import { useSP } from '@/utils/common';
import { plmApis } from '@/api';
import { useDictMap } from '@/api/querys/common';
import { ProjectWeeklyVO, WeeklyItemsVO } from '@/api/servies/plmApi';

const { TextArea } = Input;

const useGetDetailById = (id: string | number) => {
  return useQuery({
    queryKey: ['plmApis.plmWeeklyGetList', id],
    queryFn: async () => {
      const { data } = await plmApis.plmWeeklyGetList({ id } as { id: number });

      return data?.data;
    },
    enabled: Boolean(id),
  });
};

const useFillIn = () => {
  return useMutation({
    mutationFn: async (params: any) => {
      const { data } = await plmApis.plmWeeklyFillInCreate(params);

      return data;
    },
  });
};

const useWeeklyTrack = () => {
  return useMutation({
    mutationFn: async (params: any) => {
      const { data } = await plmApis.plmWeeklyTrackCreate(params);

      return data;
    },
  });
};

const CALC_FIELDS = {
  本周EV: 'currentWeekEv',
  本周累计EV: 'weekTotalEv',
  项目进度偏差率: 'paceOffset',
};

const ProjectWeeklyWrite = () => {
  const navigate = useNavigate();
  const { id, projectCode, type } = useSP();
  const IS_DISABLED = !Boolean(type);

  const { data: dictMap } = useDictMap();
  const { data: dataGetDetailById, isFetching: isFetchingGetDetailById } = useGetDetailById(id);
  const { mutate: mutateFillIn, isPending: isPendingFillIn } = useFillIn();
  const { mutate: mutateWeeklyTrack, isPending: isPendingWeeklyTrack } = useWeeklyTrack();

  const [projectOverview, setProjectOverview] = useState<ProjectWeeklyVO>({});
  const [milestoneProgress, setMilestoneProgress] = useState<WeeklyItemsVO[]>([]);
  const [description, setDescription] = useState<string>('');
  const [remark, setRemark] = useState<string>('');

  const handleOk = () => {
    // console.log(
    //   '🚀 ~ file: index.tsx:72 ~ milestoneProgress, description, remark:',
    //   milestoneProgress,
    //   description,
    //   remark,
    // );

    const findIndex = milestoneProgress.findIndex((item) => {
      return (
        item[CALC_FIELDS['项目进度偏差率'] as keyof WeeklyItemsVO] === undefined ||
        item[CALC_FIELDS['项目进度偏差率'] as keyof WeeklyItemsVO] === null
      );
    });

    if (findIndex > -1) {
      return message.warning('所有里程碑完成比例不能为空!');
    }

    const params = {
      ...projectOverview,
      remark,
      description,
      items: milestoneProgress,
    };

    // console.log(params, 'params');

    if (type === 'tracking') {
      // 跟踪
      mutateWeeklyTrack(params, {
        onSuccess: () => {
          navigate(-1);
        },
      });
    } else {
      // 填写
      mutateFillIn(params, {
        onSuccess: () => {
          navigate(-1);
        },
      });
    }
  };

  const handleCalcMilestone = (value: number | null, index: number) => {
    const _value = value || 0;
    const _milestoneProgress = [...milestoneProgress];
    const { planWorkLoad, lastWeekTotalEv, weekTotalPv } = _milestoneProgress[index];

    // 本周累积EV=计划人工工量（人日）*里程碑完成比例，保留2位小数
    const _weekTotalEv = Number(planWorkLoad || 0) * (_value / 100);

    // 本周EV=本周累积EV-上周累积EV，保留2位小数
    const _currentWeekEv = _weekTotalEv - Number(lastWeekTotalEv || 0);

    // 进度偏差率=本周累积EV/本周累积PV-1 百分比，保留2位小数
    const weekTotalPvValue = Number(weekTotalPv || 0); // Infinity || null
    const _paceOffset = weekTotalPvValue === 0 ? 0 : _weekTotalEv / weekTotalPvValue - 1;

    _milestoneProgress[index] = {
      ..._milestoneProgress[index],
      [CALC_FIELDS['本周EV']]: Number(_currentWeekEv.toFixed(2)),
      [CALC_FIELDS['本周累计EV']]: Number(_weekTotalEv.toFixed(2)),
      [CALC_FIELDS['项目进度偏差率']]: Number(_paceOffset.toFixed(2)),
    };

    setMilestoneProgress(_milestoneProgress);

    // ---------------------------------------------------------------------------------------------------------
    // 项目概况-本周累计EV
    const _all_weekTotalEv = _milestoneProgress.reduce((acc, cur) => {
      return acc + (cur[CALC_FIELDS['本周累计EV']] || 0);
    }, 0);

    // 项目概况-项目进度偏差率=本周累计ev/本周累计pv➖1
    const _allWeekTotalPvValue = _milestoneProgress.reduce((acc, cur) => {
      return acc + (cur.weekTotalPv || 0);
    }, 0);

    const _all_paceOffset =
      _allWeekTotalPvValue === 0 ? 0 : _all_weekTotalEv / _allWeekTotalPvValue - 1;

    setProjectOverview((old) => {
      return {
        ...old,
        weekTotalEv: Number(_all_weekTotalEv.toFixed(2)),
        paceOffset: Number(_all_paceOffset.toFixed(2)),
        currentWeekEv: _milestoneProgress.reduce((acc, cur) => {
          return acc + (cur[CALC_FIELDS['本周EV']] || 0);
        }, 0),
        weekTotalPv: _milestoneProgress.reduce((acc, cur) => {
          return acc + (cur.weekTotalPv || 0);
        }, 0),
      };
    });
  };

  useEffect(() => {
    if (dataGetDetailById) {
      setProjectOverview(dataGetDetailById);
      setMilestoneProgress(dataGetDetailById.items ? dataGetDetailById.items : []);

      setDescription(dataGetDetailById.description || '');
      setRemark(dataGetDetailById.remark || '');
    }
  }, [dataGetDetailById]);

  return (
    <CustomSuspense isLoading={isFetchingGetDetailById}>
      <PageHeader
        title={`周报填写（${dataGetDetailById?.weekMonth} · ${dataGetDetailById?.cycle}）`}
        onBack={() => {
          navigate(-1);
        }}
      />

      <Card title='项目概况' className='mt-18'>
        <CommonProTable
          bordered
          search={false}
          pagination={false}
          columns={[
            {
              title: '项目名称',
              dataIndex: 'projectName',
            },
            {
              title: '项目编号',
              dataIndex: 'projectCode',
            },
            {
              title: '所属交付组',
              dataIndex: 'deliveryTeam',
              valueEnum: () => dictMap.getLabelByValue['plm_deliver_teams'],
            },
            {
              title: '所属经营单元',
              dataIndex: 'managementUnit',
              valueEnum: () => dictMap.getLabelByValue['plm_teams'],
            },
            {
              title: '项目经理',
              dataIndex: 'projectManager',
              fieldProps: {
                placeholder: '按项目经理姓名模糊查询',
              },
            },
            {
              title: '上周累计EV',
              dataIndex: 'lastWeekTotalEv',
            },
            {
              title: '本周累计PV',
              dataIndex: 'weekTotalPv',
            },
            {
              title: '本周累计EV',
              dataIndex: 'weekTotalEv',
            },
            {
              title: '项目进度偏差率',
              dataIndex: 'paceOffset',
              renderText(text) {
                return text !== undefined && text !== null ? `${(text * 100).toFixed(1)}%` : '-';
              },
            },
            {
              title: '进度风险',
              dataIndex: 'paceRisk',
            },
          ]}
          dataSource={[projectOverview]}
        />
      </Card>

      <Card title='里程碑进度情况' className='mt-18'>
        <CommonProTable
          bordered
          search={false}
          pagination={false}
          columns={[
            {
              title: '里程碑名称',
              dataIndex: 'milestoneName',
            },
            {
              title: '里程碑开始日期',
              dataIndex: 'startDate',
              valueType: 'date',
              fieldProps: {
                format: 'YYYY-MM-DD',
              },
            },
            {
              title: '里程碑结束日期',
              dataIndex: 'endDate',
              valueType: 'date',
              fieldProps: {
                format: 'YYYY-MM-DD',
              },
            },
            {
              title: '计划人工工量（人日）',
              dataIndex: 'planWorkLoad',
            },
            {
              title: '上周累计EV',
              dataIndex: 'lastWeekTotalEv',
            },
            {
              title: '本周累计PV',
              dataIndex: 'weekTotalPv',
            },
            {
              title: (
                <>
                  <RequiredIcon />
                  <span>里程碑完成比例</span>
                </>
              ),
              render: (_, __, index) => {
                return (
                  <>
                    <InputNumber
                      disabled={IS_DISABLED}
                      min={0}
                      max={100}
                      precision={1}
                      className='w-[120px]'
                      addonAfter='%'
                      onChange={(value) => {
                        handleCalcMilestone(value, index);
                      }}
                    />
                  </>
                );
              },
            },
            // 前端计算
            {
              title: '本周EV',
              dataIndex: CALC_FIELDS['本周EV'],
            },
            // 前端计算
            {
              title: '本周累计EV',
              dataIndex: CALC_FIELDS['本周累计EV'],
            },
            // 前端计算
            {
              title: '进度偏差率',
              dataIndex: CALC_FIELDS['项目进度偏差率'],
              renderText(text) {
                return text !== undefined && text !== null ? `${(text * 100).toFixed(1)}%` : '-';
              },
            },
          ]}
          dataSource={milestoneProgress}
        />
      </Card>

      <Card title='当前进展描述' className='mt-18'>
        <TextArea
          disabled={IS_DISABLED}
          rows={6}
          maxLength={1000}
          showCount
          placeholder='请输入'
          value={description}
          onChange={(e) => {
            setDescription(e.target.value);
          }}
        />
      </Card>

      <RiskTrackingRecord
        allDisabled={IS_DISABLED}
        projectCode={projectCode}
        cardProps={{
          className: 'mt-18',
        }}
      />

      <Card title='备注' className='mt-18'>
        <TextArea
          disabled={IS_DISABLED}
          rows={6}
          maxLength={1000}
          showCount
          placeholder='请输入'
          value={remark}
          onChange={(e) => {
            setRemark(e.target.value);
          }}
        />
      </Card>

      <div className='mt-32 flex justify-end'>
        <Button
          className='mr-10'
          onClick={() => {
            navigate(-1);
          }}
        >
          返回
        </Button>
        {!IS_DISABLED && (
          <Button
            type='primary'
            className='px-24'
            loading={isPendingFillIn || isPendingWeeklyTrack}
            onClick={handleOk}
          >
            保存
          </Button>
        )}
      </div>
    </CustomSuspense>
  );
};

export default ProjectWeeklyWrite;
