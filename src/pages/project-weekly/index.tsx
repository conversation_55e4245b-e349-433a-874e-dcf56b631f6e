import { useRef } from 'react';

import type { ActionType, FormInstance, ProColumns } from '@ant-design/pro-components';
import { CommonProTable } from '@/common/common-view';
import { Button, message, Space } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';

import { plmApis } from '@/api';
import { useNavigate } from 'react-router-dom';
import { useDictMap, useDownload, useSystemMenu } from '@/api/querys/common';
import { getDataFromLocalStorage } from '@/utils';
import { formatSearch } from '@/utils/common';
import { useMutation } from '@tanstack/react-query';
import { AuthButton } from '@hbwow/components';

const useLoadUsingGet = () => {
  return useMutation({
    mutationFn: plmApis.plmWeeklyLoadList,
  });
};

const ProjectWeekly = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const navigate = useNavigate();
  const { mutate: mutateDownload, isPending: isPendingDownload } = useDownload();
  const { data: dictMap } = useDictMap();
  const { mutate: mutateLoadUsingGet, isPending: isPendingLoadUsingGet } = useLoadUsingGet();
  const curBeforeGoId = useRef({ id: '', type: '' });
  const { data: dataSystemMenu } = useSystemMenu();

  // 跳转前校验
  const beforeGo = (id: string | number, onSuccess: () => void) => {
    mutateLoadUsingGet({ id } as { id: number }, {
      onSuccess: () => {
        onSuccess?.();
      },
    });
  };

  const columns: ProColumns[] = [
    {
      title: '项目',
      dataIndex: 'projectCode',
      hideInTable: true,
      order: 999,
      fieldProps: {
        placeholder: '按项目名称或编号模糊查询',
      },
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
      fixed: 'left',
      ellipsis: true,
      hideInSearch: true,
      renderText: (text, record) => {
        const { id, projectCode } = record;

        return (
          <Button
            type='link'
            className='hover:underline'
            onClick={() => {
              navigate({
                pathname: '/projectProgress/projectWeekly/write',
                search: formatSearch({ id, projectCode }),
              });
            }}
          >
            {text}
          </Button>
        );
      },
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
      hideInSearch: true,
    },
    {
      title: '所属交付组',
      dataIndex: 'deliveryTeam',
      valueEnum: () => dictMap.getLabelByValue['plm_deliver_teams'],
    },
    {
      title: '所属经营单元',
      dataIndex: 'managementUnit',
      valueEnum: () => dictMap.getLabelByValue['plm_teams'],
    },
    {
      title: '项目经理',
      dataIndex: 'projectManager',
      fieldProps: {
        placeholder: '按项目经理姓名模糊查询',
      },
      order: 996,
    },
    {
      title: '周报周期',
      dataIndex: 'startDate',
      valueType: 'date',
      fieldProps: {
        format: 'YYYY-MM-DD',
      },
      hideInTable: true,
      order: 998,
    },
    {
      title: '周报周期',
      dataIndex: 'cycle',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '所属月份',
      dataIndex: 'weekMonth',
      hideInSearch: true,
    },
    {
      title: '周报填写时间',
      dataIndex: 'fillInTime',
      valueType: 'date',
      fieldProps: {
        format: 'YYYY-MM-DD HH:mm:ss',
      },
      hideInSearch: true,
    },
    {
      title: '本周累计PV',
      dataIndex: 'weekTotalPv',
      hideInSearch: true,
    },
    {
      title: '本周累计EV',
      dataIndex: 'weekTotalEv',
      hideInSearch: true,
    },
    {
      title: '进度风险',
      dataIndex: 'paceRisk',
      hideInSearch: true,
    },
    {
      title: '风险数量',
      dataIndex: 'riskNum',
      hideInSearch: true,
    },
    {
      title: '周报状态',
      dataIndex: 'weeklyState',
      fixed: 'right',
      width: 100,
      order: 997,
      valueEnum: () => dictMap.getLabelByValue['plm_weekly_state'],
      renderText(text) {
        if (text === 0) {
          return (
            <span className='text-red-500'>
              {dictMap.getLabelByValue['plm_weekly_state'][text]}
            </span>
          );
        }

        return text;
      },
    },
    {
      title: '操作',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 140,
      render: (_, record) => {
        const { id, projectCode, weeklyState } = record;

        return (
          <Space>
            <AuthButton
              authId='projectWeekly_write'
              authList={dataSystemMenu?.perms}
              loading={
                isPendingLoadUsingGet &&
                curBeforeGoId.current.id === id &&
                curBeforeGoId.current.type === 'write'
              }
              disabled={weeklyState === 2}
              type='link'
              onClick={() => {
                const TYPE = 'write';
                curBeforeGoId.current = { id, type: TYPE };

                beforeGo(id, () => {
                  navigate({
                    pathname: '/projectProgress/projectWeekly/write',
                    search: formatSearch({ id, projectCode, type: TYPE }),
                  });
                });
              }}
            >
              填写
            </AuthButton>
            <AuthButton
              authId='projectWeekly_track'
              authList={dataSystemMenu?.perms}
              loading={
                isPendingLoadUsingGet &&
                curBeforeGoId.current.id === id &&
                curBeforeGoId.current.type === 'tracking'
              }
              disabled={weeklyState === 2}
              type='link'
              onClick={() => {
                const TYPE = 'tracking';
                curBeforeGoId.current = { id, type: TYPE };

                beforeGo(id, () => {
                  navigate({
                    pathname: '/projectProgress/projectWeekly/write',
                    search: formatSearch({ id, projectCode, type: TYPE }),
                  });
                });
              }}
            >
              跟踪
            </AuthButton>
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <CommonProTable
        storeKey='projectProgress/projectWeekly'
        // scroll={{ x: 2000 }}
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        pageRequest={plmApis.plmWeeklyPageCreate}
        headerTitle={
          <div>
            <>项目周报</>
          </div>
        }
        toolBarRender={() => [
          <AuthButton
            authId='projectWeekly_export'
            authList={dataSystemMenu?.perms}
            key='2'
            icon={<DownloadOutlined />}
            loading={isPendingDownload}
            onClick={() => {
              mutateDownload(
                {
                  reqUrl: '/pmt/plm/weekly/export',
                  method: 'POST',
                  token: `Bearer ${getDataFromLocalStorage('authorization')}`,
                  data: {
                    ...(formRef.current?.getFieldsValue() || {}),
                  },
                },
                {
                  onError(err) {
                    message.error('导出失败：' + err.message);
                  },
                },
              );
            }}
          >
            导出
          </AuthButton>,
        ]}
      />
    </>
  );
};

export default ProjectWeekly;
