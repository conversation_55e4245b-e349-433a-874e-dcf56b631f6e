import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { useRef } from 'react';
import './index.less';
import { plmbaseApis } from '@/api';
import { CommonProTable, CustomUpload } from '@/common/common-view';
import { useDictStore } from '@/store/dictionary';

const ResourceStatusUsage = () => {
  const actionRef = useRef<ActionType>();
  const { allDicts } = useDictStore();

  const columns: ProColumns[] = [
    {
      title: '工号',
      dataIndex: 'jobNumber',
    },
    {
      title: '姓名',
      dataIndex: 'realName',
    },
    {
      title: '交付组',
      dataIndex: 'teamId',
      hideInTable: true,
      hideInSearch: true,
      valueEnum: () => allDicts['plm_deliver_teams'],
    },
    {
      title: '交付组',
      dataIndex: 'teamName',
      hideInSearch: true,
    },
    {
      title: '岗位',
      dataIndex: 'jobId',
      hideInTable: true,
      hideInSearch: true,
      valueEnum: () => allDicts['plm_jobs'],
    },
    {
      title: '岗位',
      dataIndex: 'jobName',
      hideInSearch: true,
    },
    {
      title: '月份',
      dataIndex: 'yearMonths',
      hideInSearch: true,
    },
    {
      title: '项目编号',
      dataIndex: 'projectCode',
    },
    {
      title: '项目简称',
      dataIndex: 'projectName',
    },
    {
      title: '所属年度',
      dataIndex: 'projectYear',
      hideInSearch: true,
    },
    {
      title: '投入度',
      dataIndex: 'capacity',
      hideInSearch: true,
      renderText: text => `${text}%`,
    },
  ];
  return (
    <>
      <CommonProTable
        columns={columns}
        actionRef={actionRef}
        pageRequest={(para: any) =>
          plmbaseApis.pageUsingPost11({ ...para, dataType: 2 })
        }
        headerTitle={null ?? '资源月度投入'}
        toolBarRender={() => [
          <CustomUpload
            btnName="导入月度投入"
            url="/pmt/staff/wl/importData"
            data={{ dt: 2 }}
            callback={() => actionRef.current?.reload()}
          />,
        ]}
      />
    </>
  );
};

export default ResourceStatusUsage;
