import { useEffect } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';

import { Descriptions, Form, Modal, ModalProps, Select } from 'antd';
import { CustomSuspense } from '@hbwow/components';

import { plmApis } from '@/api';
import { useDictOptions } from '@/api/querys/common';
import { useGetRules } from '@hbwow/validate-antd';

interface ICUProps {
  id?: string | number;
  onOkCallback?: () => void;
  modalProps?: ModalProps;
}

const useGetDetailById = (id: number) => {
  return useQuery({
    queryKey: ['plmApis.plmPiGetList', id],
    queryFn: async () => {
      const res = await plmApis.plmPiGetList({
        id,
      });

      return res.data.data;
    },
    enabled: Boolean(id),
  });
};

const useUpdate = () => {
  return useMutation({
    mutationFn: async (params: any) => {
      const res = await plmApis.plmPiUpdateCreate({
        ...params,
      });
      return res.data;
    },
  });
};

const CU = ({ id = '', onOkCallback, modalProps = {} }: ICUProps) => {
  const [form] = Form.useForm();
  const { getRules } = useGetRules();
  const { data: dataDictOptions, isFetching: isFetchingDictOptions } = useDictOptions();

  const { data: dataDetail, isFetching: isFetchingDetail } = useGetDetailById(id as number);
  const { mutateAsync: mutateAsyncUpdate, isPending: isPendingUpdata } = useUpdate();

  useEffect(() => {
    if (Boolean(dataDetail)) {
      form.setFieldsValue(dataDetail);
    }
  }, [dataDetail]);

  return (
    <Modal
      title={id ? '编辑项目' : '新增项目'}
      confirmLoading={isPendingUpdata}
      onOk={async () => {
        const formdata = await form.validateFields();

        const params = {
          id: id as number,
          ...formdata,
        };

        await mutateAsyncUpdate({
          ...params,
        });

        onOkCallback?.();
      }}
      afterClose={() => {
        form.resetFields();
      }}
      {...modalProps}
    >
      <CustomSuspense isFetching={isFetchingDetail}>
        <Descriptions
          column={1}
          items={[
            { label: '项目简称', children: dataDetail?.projectName },
            { label: '项目编号', children: dataDetail?.projectCode },
          ]}
        />

        <Form form={form} className='mt-24'>
          <Form.Item
            label='项目状态'
            name='projectState'
            rules={[...getRules({ name: 'isNullOrUndefined' })]}
          >
            <Select
              options={dataDictOptions['plm_project_state']}
              loading={isFetchingDictOptions}
            />
          </Form.Item>
        </Form>
      </CustomSuspense>
    </Modal>
  );
};

export default CU;
