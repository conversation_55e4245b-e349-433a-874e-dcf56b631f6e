import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { useRef } from 'react';
import './index.less';
import { plmbaseApis } from '@/api';
import { CommonProTable, CustomUpload } from '@/common/common-view';
import { useDictStore } from '@/store/dictionary';

const ResourceStatusDuty = () => {
  const actionRef = useRef<ActionType>();
  const { allDicts } = useDictStore();

  const columns: ProColumns[] = [
    {
      title: '工号',
      dataIndex: 'jobNumber',
    },
    {
      title: '姓名',
      dataIndex: 'realName',
    },
    {
      title: '交付组',
      dataIndex: 'teamId',
      hideInTable: true,
      valueEnum: () => allDicts['plm_deliver_teams'],
    },
    {
      title: '交付组',
      dataIndex: 'teamName',
      hideInSearch: true,
    },
    {
      title: '岗位',
      dataIndex: 'jobId',
      hideInTable: true,
      valueEnum: () => allDicts['plm_jobs'],
    },
    {
      title: '岗位',
      dataIndex: 'jobName',
      hideInSearch: true,
    },
    {
      title: '月份',
      dataIndex: 'yearMonths',
      hideInSearch: true,
    },
    {
      title: '在岗状态',
      dataIndex: 'workState',
      valueEnum: () => allDicts['plm_work_state'],
    },
    {
      title: '可用度',
      dataIndex: 'workLoad',
      hideInSearch: true,
      renderText: text => `${text}%`,
    },
  ];

  return (
    <>
      <CommonProTable
        columns={columns}
        actionRef={actionRef}
        pageRequest={(para: any) => plmbaseApis.pageUsingPost12(para)}
        headerTitle={null ?? '资源在岗状态'}
        toolBarRender={() => [
          <CustomUpload
            btnName="导入在岗状态"
            url="/pmt/staff/ws/importData"
            callback={() => actionRef.current?.reload()}
          />,
        ]}
      />
    </>
  );
};

export default ResourceStatusDuty;
