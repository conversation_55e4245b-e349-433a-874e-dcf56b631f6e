import { useRoutes } from 'react-router-dom';
import Layout from '@/layout/index';
import Home from '@/pages/home';

import ProjectOverview from '@/pages/project-overview';
import MilestonePlan from '@/pages/milestone-plan';
import ProjectMonthlyPlan from '@/pages/project-monthly-plan';
import ProjectMonthlyProgress from '@/pages/project-monthly-progress';
import ProjectWeekly from '@/pages/project-weekly';
import ProjectWeeklyWrite from '@/pages/project-weekly/write';

// import ProjectMonthlyCost from '@/pages/project-monthly-cost';
// import ResourceOverview from '@/pages/resource-overview';
// import ResourceStatusDuty from '@/pages/resource-status-duty';
// import ResourceStatusLoad from '@/pages/resource-status-load';
// import ResourceStatusUsage from '@/pages/resource-status-usage';
// import ResourceJobLevel from '@/pages/resource-job-level';

export const routes = [
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        path: '/',
        name: '首页',
        hideInMenu: true,
        element: <Home />,
      },
      {
        name: '项目总览',
        path: '/projectManager/overview',
        element: <ProjectOverview />,
      },
      {
        name: '项目计划',
        path: '/projectPlan',
        children: [
          {
            name: '里程碑计划',
            path: '/projectPlan/milestonePlan',
            element: <MilestonePlan />,
          },
          {
            name: '项目月度计划',
            path: '/projectPlan/monthlyPlan',
            element: <ProjectMonthlyPlan />,
          },
        ],
      },
      {
        name: '项目进展',
        path: '/projectProgress',
        children: [
          {
            name: '月度进展',
            path: '/projectProgress/monthlyProgress',
            element: <ProjectMonthlyProgress />,
          },
          {
            name: '项目周报',
            path: '/projectProgress/projectWeekly',
            element: <ProjectWeekly />,
          },
          {
            path: '/projectProgress/projectWeekly/write',
            element: <ProjectWeeklyWrite />,
            name: '周报填写',
            hideInMenu: true,
          },
        ],
      },
      // {
      //   name: '项目数据管理',
      //   path: '/projectManager',
      //   children: [
      //     {
      //       name: '项目总览',
      //       path: '/projectManager/overview',
      //       element: <ProjectOverview />,
      //     },
      //     {
      //       name: '项目月度计划',
      //       path: '/projectManager/monthlyPlan',
      //       element: <ProjectMonthlyPlan />,
      //     },
      //     {
      //       name: '项目月度进度',
      //       path: '/projectManager/monthlyProgress',
      //       element: <ProjectMonthlyProgress />,
      //     },
      //     {
      //       name: '项目月度成本',
      //       path: '/projectManager/monthlyCost',
      //       element: <ProjectMonthlyCost />,
      //     },
      //   ],
      // },
      // {
      //   name: '资源数据管理',
      //   path: '/resourceManager',
      //   children: [
      //     {
      //       name: '资源总览',
      //       path: '/resourceManager/overview',
      //       element: <ResourceOverview />,
      //     },
      //     {
      //       name: '资源在岗状态',
      //       path: '/resourceManager/statusDuty',
      //       element: <ResourceStatusDuty />,
      //     },
      //     {
      //       name: '资源负载状态',
      //       path: '/resourceManager/statusLoad',
      //       element: <ResourceStatusLoad />,
      //     },
      //     {
      //       name: '资源投入状态',
      //       path: '/resourceManager/statusUsage',
      //       element: <ResourceStatusUsage />,
      //     },
      //     {
      //       name: '资源岗级总览',
      //       path: '/resourceManager/jobLevel',
      //       element: <ResourceJobLevel />,
      //     },
      //   ],
      // },
    ],
  },
];

//根据path获取route数据
export const getRouteByPath = (path: string, rs?: any): any => {
  const temps = rs ?? routes;
  for (let i = 0; i < temps.length; i++) {
    const route = temps[i];
    if (route.path === path) {
      return route;
    }
    const r: any = getRouteByPath(path, route.children ?? []);
    if (r !== undefined) {
      return r;
    }
  }
};

const Router = () => {
  const element = useRoutes(routes);
  return element;
};
export default Router;
