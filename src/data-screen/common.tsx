import { getBaseResult } from '@/api';
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import * as echarts from 'echarts';

export const getLegend = (data?: any) => {
  if (data) {
    data = data.map((v: any) => {
      return {
        name: v.name,
        itemStyle: v.icon == 'line' || v.icon == 'dashed' ? { opacity: 0 } : {},
      };
    });
  }
  return {
    data: data,
    itemWidth: 10,
    itemHeight: 10,
    right: '0%',
    top: '5',
    textStyle: {
      color: 'rgba(255, 255, 255, 0.60)',
      fontSize: 12,
    },
  };
};

export const commonGrid = {
  grid: {
    left: 40,
    right: 10,
    top: 70,
    bottom: 20,
  },
};

const commonTitleStyle = {
  title: {
    textStyle: { color: '#fff', fontSize: 14, lineHeight: 22 },
  },
};

export const splitLine = {
  lineStyle: {
    color: 'rgba(255, 255, 255, 0.10)',
  },
};

export const monthXAxis = (boundaryGap: boolean) => {
  return {
    type: 'category',
    boundaryGap,
    data: [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月',
    ],
    axisLabel: {
      interval: 0,
    },
    axisTick: { show: false },
  };
};

export const MyEchart: FC<any> = (props: {
  option: any;
  data: any;
  className: any;
  style: any;
}) => {
  const { option: chartOptions, data, className, style } = props;
  const dom = useRef<any>();
  const chart = useRef<any>();
  useEffect(() => {
    if (chartOptions) {
      chart.current = echarts.init(dom.current);
      chart.current.setOption(commonTitleStyle);
      chart.current.setOption(chartOptions);
    }
  }, [chartOptions != undefined]);

  useEffect(() => {
    if (data && chart.current) {
      chart.current.setOption(data);
    }
  }, [data]);

  return <div className={className} ref={dom} style={style}></div>;
};

function useMemoizedFn(fn: any) {
  const fnRef = useRef<any>(fn);
  fnRef.current = useMemo<any>(() => fn, [fn]);

  const memoizedFn = useRef<any>();
  if (!memoizedFn.current) {
    memoizedFn.current = function (this: any, ...args: any) {
      return fnRef.current.apply(this, args);
    };
  }

  return memoizedFn.current;
}

export const useInterval = (
  fn: () => void,
  delay?: number,
  options: { immediate?: boolean; refreshToken?: any } = {},
) => {
  const timerCallback = useMemoizedFn(fn);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);

  const clear = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  }, []);

  useEffect(() => {
    if (!Number(delay) || (delay ?? 0) < 0) {
      return;
    }
    if (options.immediate) {
      timerCallback();
    }
    timerRef.current = setInterval(timerCallback, delay);
    return clear;
  }, [delay, options.immediate, options?.refreshToken]);

  return clear;
};

export const useIntervalRequest = (
  request: () => Promise<any>,
  resultCallback: (data: any) => void,
  options?: {
    delay?: number;
    refreshToken?: any;
  },
) => {
  const rc = useMemoizedFn(resultCallback);
  return useInterval(
    () => getBaseResult(request()).then(data => rc(data)),
    options?.delay ?? 2 * 60 * 1000,
    // delay ?? 5 * 1000,
    { immediate: true, refreshToken: options?.refreshToken },
  );
};

export const MyScrollTabChart: FC<any> = (props: {
  option: any;
  tabData: Array<string>;
  data: Array<Array<any>>;
  className: any;
  style: any;
}) => {
  const { option, tabData, data, className, style } = props;
  const tableMargin = 10;
  const headHeight = 25;
  const cellWidth = 60;
  let dataIndex = 0;
  let doTransform = false;
  const transformDuration = 300;
  const op = {
    ...option,
    grid: [
      { left: 0, top: 30 + tableMargin, right: 0 },
      { left: 45, top: 30 + tableMargin + headHeight + 10, bottom: 20 },
    ],
    xAxis: [
      { gridIndex: 0, show: false },
      { gridIndex: 1, ...option.xAxis },
    ],
    yAxis: [
      { gridIndex: 0, show: false },
      ...option.yAxis.map((v: any) => {
        return { gridIndex: 1, ...v };
      }),
    ],
    series: [
      {
        name: 'header',
        type: 'custom',
        clip: true,
        xAxisIndex: 0,
        yAxisIndex: 0,
        renderItem: () => {
          return {
            type: 'group',
            y: headHeight,
            children: tabData.map((v, index) => {
              return {
                type: 'rect',
                x: cellWidth * (doTransform ? index - 1 : index),
                y: tableMargin,
                shape: {
                  width: cellWidth,
                  height: headHeight,
                },
                style: {
                  fill: undefined,
                },
                textContent: {
                  style: {
                    text: tabData[(index + dataIndex) % tabData.length],
                    fill: (doTransform ? index == 1 : index == 0)
                      ? '#fff'
                      : 'rgba(255, 255, 255, 0.60)',
                  },
                },
                textConfig: {
                  position: 'inside',
                },
                transition: doTransform ? ['x'] : [],
                updateAnimation: { duration: transformDuration },
              };
            }),
          };
        },
      },
      ...option.series.map((v: any) => {
        return { ...v, xAxisIndex: 1, yAxisIndex: (v.yAxisIndex ?? 0) + 1 };
      }),
    ],
  };
  const dom = useRef<any>();
  const chart = useRef<any>();
  useEffect(() => {
    chart.current = echarts.init(dom.current);
    chart.current.setOption(commonTitleStyle);
  }, []);

  useEffect(() => {
    chart.current.setOption(op);
    chart.current.setOption({
      series: [
        { data: tabData.length > 0 ? [tabData] : [] },
        ...(data.length > 0 ? data[0] : [{ data: [] }, { data: [] }]),
      ],
    });
    if (tabData.length == 0) {
      return;
    }
    let toId: any = undefined;
    const id = setInterval(() => {
      doTransform = true;
      chart.current.setOption({});
      toId = setTimeout(() => {
        doTransform = false;
        dataIndex = ++dataIndex % data.length;
        chart.current.setOption({
          series: [{}, ...(data.length > 0 ? data[dataIndex] : [])],
        });
      }, transformDuration + 50);
    }, 6000);
    return () => {
      clearInterval(id);
      toId && clearTimeout(toId);
    };
  }, [JSON.stringify(data)]);

  return <div ref={dom} className={className} style={style}></div>;
};

export const MyScrollTable: FC<any> = (props: {
  title: string;
  headData: Array<string>;
  data: Array<Array<any>>;
  className: any;
  style: any;
}) => {
  const { title, headData, data, className, style } = props;
  const tableMargin = 40;
  const rowHeight = 25;
  let count = 0;
  const option = {
    title: {
      ...commonTitleStyle.title,
      text: title,
    },
    xAxis: [
      { gridIndex: 0, show: false },
      { gridIndex: 1, show: false },
    ],
    yAxis: [
      { gridIndex: 0, show: false },
      { gridIndex: 1, show: false },
    ],
    grid: [
      { left: 0, top: tableMargin, right: 0 },
      { left: 0, top: tableMargin + rowHeight, right: 0, bottom: 0 },
    ],
    series: [
      {
        name: 'header',
        type: 'custom',
        clip: true,
        renderItem: (params: any, api: any) => {
          const cellWidth = api.getWidth() / headData.length;
          return {
            type: 'group',
            y: tableMargin - rowHeight,
            children: [...Array(headData.length)].map((_, index) => {
              return {
                type: 'rect',
                x: cellWidth * index,
                y: rowHeight * (params.dataIndex + 1),
                shape: {
                  width: cellWidth,
                  height: rowHeight,
                },
                style: {
                  fill: 'rgba(3, 195, 255, 0.20)',
                },
                textContent: {
                  style: {
                    text: headData[index],
                    fill: 'rgba(165, 211, 210, 0.60)',
                  },
                },
                textConfig: {
                  position: 'inside',
                },
              };
            }),
          };
        },
        data: [headData],
      },
      {
        name: 'data',
        type: 'custom',
        clip: true,
        xAxisIndex: 1,
        yAxisIndex: 1,
        silent: true,
        renderItem: (params: any, api: any) => {
          if (params.dataIndex == 0) {
            count++;
          }
          const cellWidth = api.getWidth() / headData.length;
          return {
            type: 'group',
            y: tableMargin,
            children: [...Array(headData.length)].map((_, index) => {
              return {
                type: 'rect',
                x: cellWidth * index,
                y: rowHeight * (params.dataIndex + 1),
                shape: {
                  width: cellWidth,
                  height: rowHeight,
                },
                style: {
                  fill:
                    (params.dataIndex + (count > 1 ? count : 0)) % 2 == 1
                      ? 'rgba(3, 195, 255, 0.05)'
                      : undefined,
                },
                textContent: {
                  style: {
                    text: data[params.dataIndexInside][index],
                    fill: '#fff',
                  },
                },
                textConfig: {
                  position: 'inside',
                },
                keyframeAnimation:
                  count > 1
                    ? [
                        {
                          duration: 200,
                          keyframes: [
                            {
                              percent: 0,
                              y: rowHeight * (params.dataIndex + 1),
                            },
                            {
                              percent: 1,
                              y: rowHeight * (params.dataIndex + 1) - rowHeight,
                            },
                          ],
                        },
                      ]
                    : undefined,
              };
            }),
          };
        },
        data: data,
      },
    ],
  };
  const dom = useRef<any>();
  const chart = useRef<any>();
  useEffect(() => {
    chart.current = echarts.init(dom.current);
    chart.current.setOption(option);
  }, []);

  useEffect(() => {
    chart.current.setOption({ ...option });
    if (data.length > 5) {
      const id = setInterval(() => {
        chart.current.setOption({
          series: [
            {
              name: 'data',
              data: data,
            },
          ],
        });
        if (data && data.length > 0) {
          data.push(data.shift() as any);
        }
      }, 2000);
      return () => clearInterval(id);
    }
  }, [JSON.stringify(data)]);

  return <div ref={dom} className={className} style={style}></div>;
};

export const myFixed = (v: any, len = 2) => {
  try {
    len = isNaN(len) ? 0 : len;
    const num = Math.pow(10, len);
    const r = Math.round(v * num) / num;
    return Number.isNaN(r) ? '-' : r;
  } catch (e) {
    console.log(e);
  }
  return '-';
};

export const MyDropdown = ({
  options,
  defaultV,
  callback,
}: {
  options: Array<any>;
  defaultV: string;
  callback: (v: string) => void;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(defaultV ?? '');
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };
  const handleOptionSelect = (option: any) => {
    setSelectedOption(option);
    setIsOpen(false);
    callback?.(option);
  };
  return (
    <div className="relative w-[87px]">
      <div
        onClick={toggleDropdown}
        className="cursor-pointer text-[20px] text-[#ECF7FF]"
      >
        {selectedOption}
        <svg
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
          className="ml-[8px]"
        >
          <path d="M10 3H2L6.16 9L10 3Z" fill="#ECF7FF" />
        </svg>
      </div>
      {isOpen && (
        <ul className="absolute bg-white border border-gray-300 rounded shadow-lg mt-1 w-full z-10 p-0">
          {options.map((option: any, index: any) => (
            <li
              key={index}
              onClick={() => handleOptionSelect(option)}
              className="hover:bg-gray-200 cursor-pointer px-4 py-2 block"
            >
              {option}
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default {};
