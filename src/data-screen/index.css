@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

#page {
  background-image: url('/assets/page-bg.png');
}

.header {
  height: 80px;
  background-image: url('/assets/page-header.png');
  background-size: 100% 100%;
}

.area {
  background: linear-gradient(
    90deg,
    rgba(24, 144, 255, 0.1) 37.07%,
    rgba(24, 144, 255, 0) 99.96%
  );
}

.title {
  background-repeat: no-repeat;
  background-size: cover;
  height: 48px;
  padding-left: 60px;
  color: #fff;
  font-family: '<PERSON><PERSON><PERSON>';
  font-size: 16px;
  font-style: normal;
  font-weight: 700;
  line-height: 48px;
  letter-spacing: 1.6px;
  background-size: contain;
}

.title-long {
  background-image: url('/assets/area-header-long.png');
  background-size: 100% 100%;
}

.title-short {
  background-image: url('/assets/area-header.png');
  background-size: 100% 100%;
}

.bloc-bg {
  border-radius: 2px;
  background: rgba(54, 151, 225, 0.08);
  padding: 16px;
}

.sub-info-text {
  color: #ecf7ff;
  margin-right: 6px;
  text-align: right;
  text-shadow: 4px 4px 16px rgba(146, 209, 255, 0.46);
  font-family: 'DingTalk Sans';
  font-size: 24px;
  letter-spacing: 3.84px;
}

.sub-info-unit {
  color: #ecf7ff;
  text-align: center;
  font-family: 'Source Han Sans SC';
  font-size: 12px;
}

.sub-info-desc {
  color: rgba(255, 255, 255, 0.4);
  text-align: center;
  font-size: 12px;
  margin: 6px;
}

.times {
  font-family: 'ChakraPetch-Bold';
}

@font-face {
  font-family: 'ChakraPetch-Bold';
  src: url('/assets/subset-ChakraPetch-Bold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}
