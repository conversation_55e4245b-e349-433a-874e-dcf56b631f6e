import { useEffect, useState } from 'react';
import './index.css';
import { getBaseResult, screenApis } from '@/api';
import { StaffLargeScreenVO } from '@/api/servies/screenApi';
import {
  commonGrid,
  getLegend,
  monthXAxis,
  MyDropdown,
  MyEchart,
  myFixed,
  MyScrollTabChart,
  MyScrollTable,
  splitLine,
  useInterval,
  useIntervalRequest,
} from './common';

import ReactDOM from 'react-dom/client';

let allDist: any = {};
let curYear = `${new Date().getFullYear()}`;

//项目总览
const ProjectsView = () => {
  const [data, setData] = useState<any>({});

  const labelcfg = {
    label: {
      color: '#fff',
      fontWeight: 400,
      fontSize: 10,
      overflow: 'breakAll',
    },
    labelLine: {
      length: 8,
      length2: 5,
    },
  };

  const levelsOp = {
    title: {
      text: '项目\n分级',
      left: 'center',
      top: 'center',
    },
    series: [
      {
        type: 'pie',
        // radius: ['40%', '60%'],
        radius: ['30%', '50%'],
      },
    ],
  };

  const stagesOp = {
    title: {
      text: '项目\n阶段',
      left: 'center',
      top: 'center',
    },
    series: [
      {
        type: 'pie',
        // radius: ['40%', '60%'],
        radius: ['30%', '50%'],
      },
    ],
  };

  const demandsOp = {
    ...commonGrid,
    title: {
      text: '交付工量需求（季度）',
    },
    legend: getLegend(),
    xAxis: {
      type: 'category',
      axisTick: { show: false },
      data: ['第一季度', '第二季度', '第三季度', '第四季度'],
    },
    yAxis: {
      name: '人月',
      // interval: 100,
      splitLine,
    },
    series: [
      {
        name: '计划人工工量',
        type: 'bar',
        barWidth: 20,
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 14,
          fontWeight: 700,
        },
        itemStyle: {
          color: '#2769DB',
        },
      },
      {
        name: '计划编码工量',
        type: 'bar',
        barWidth: 20,
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 14,
          fontWeight: 700,
        },
        itemStyle: {
          color: '#66D8FF',
        },
      },
    ],
  };

  useIntervalRequest(
    () => screenApis.projectOverviewUsingGet({ projectYear: curYear }),
    data => {
      if (!data) {
        return;
      }
      if (data.priority) {
        const d = data.priority;
        const process = (n: number, name: any, color?: any) => {
          return {
            value: n,
            name: `${name}级：${n}个`,
            itemStyle: { color: color },
            ...labelcfg,
          };
        };
        data.priority = [
          process(d.oneLevel, '一'),
          process(d.twoLevel, '二'),
          process(d.threeLevel, '三'),
          process(d.fourLevel, '四'),
        ];
      }
      if (data.stages) {
        const dist: any = {};
        allDist['plm_project_stage']?.forEach((e: any) => {
          dist[e.dictValue] = e.dictLabel;
        });
        data.stages = data.stages.map((v: any) => {
          return v.projectCount > 0
            ? {
                value: v.projectCount,
                name: `${dist[v.projectStage]}：${v.projectCount}个`,
                ...labelcfg,
              }
            : {};
        });
      }
      if (data.delivers) {
        data.delivers = [
          {
            data: data.delivers.map((v: any) => myFixed(v.planWorkLoad, 0)), //计划人工工量
          },
          {
            data: data.delivers.map((v: any) =>
              myFixed(v.planCodingWorkLoad, 0),
            ), //计划编码工量
          },
        ];
      }
      setData({ ...data });
    },
    { refreshToken: curYear },
  );

  const subInfo = (no?: number, unit?: string, desc?: string) => {
    return (
      <>
        <div className="felx">
          <div className="flex items-baseline justify-center">
            <div className="sub-info-text">
              {myFixed(no, 2)?.toString() ?? '-'}
            </div>
            <div className="sub-info-unit">{unit}</div>
          </div>
          <p className="sub-info-desc justify-center">{desc}</p>
        </div>
      </>
    );
  };

  return (
    <div className="flex h-full w-full flex-col">
      <div className="title title-short">项目总览</div>
      <div className="area grow grid grid-cols-2 grid-rows-[87px_minmax(0,_1fr)_minmax(0,_1fr)] gap-[16px] p-[16px]">
        <div className="grid grid-cols-3 gap-[24px] bloc-bg col-span-2">
          {subInfo(data.overview?.totalQuantity, '个', '上年度转结及新增项目')}
          {subInfo(data.overview?.planWorkLoad, '万元', '年度预计人工成本')}
          {subInfo(
            data.overview?.codingWorkCost,
            '万元',
            '年度预计编码人工成本',
          )}
        </div>
        <MyEchart
          option={levelsOp}
          data={{ series: [{ data: data.priority }] }}
          className="bloc-bg p-[0]"
        />
        <MyEchart
          option={stagesOp}
          data={{ series: [{ data: data.stages }] }}
          className="bloc-bg p-[0]"
        />
        <MyEchart
          option={demandsOp}
          data={{ series: data.delivers }}
          className="bloc-bg col-span-2"
        />
      </div>
    </div>
  );
};

// 项目进度分析
const ProjectsAnalyzeView = () => {
  const progCmpleteOp = {
    ...commonGrid,
    title: {
      text: '进度完成情况',
    },
    legend: getLegend([
      { name: '计划编码工量', icon: 'line' },
      { name: '完成编码工量', icon: 'line' },
    ]),
    xAxis: monthXAxis(false),
    yAxis: {
      name: '人月',
      type: 'value',
      splitLine,
    },
    series: [
      {
        name: '计划编码工量',
        type: 'line',
        smooth: true,
        // symbolSize: 6,
        showSymbol: false,
        // lineStyle: {
        //   width: 3,
        // },
        itemStyle: {
          // color: '#FFDF80',
          color: '#2EF5C6',
        },
        data: [],
      },
      {
        name: '完成编码工量',
        type: 'line',
        smooth: true,
        showSymbol: false,
        itemStyle: {
          // color: '#FF8C00',
          color: '#FFDF80',
        },
        data: [],
      },
    ],
  };
  const progComplianceOp = {
    title: {
      text: '各经营单元进度符合度',
    },
    legend: getLegend([
      { name: '进度符合度', icon: 'line' },
      { name: '产出量缺口' },
    ]),
    xAxis: monthXAxis(true),
    yAxis: [
      {
        type: 'value',
        // name: '进度符合度',
        min: 40,
        max: 160,
        interval: 20,
        splitLine,
        axisLabel: {
          formatter: '{value}%',
        },
      },
      {
        type: 'value',
        // name: '月完成情况',
        min: 0,
        max: 6,
        interval: 1,
        splitLine,
      },
    ],
    series: [
      {
        name: '进度符合度',
        type: 'line',
        showSymbol: false,
        smooth: true,
        itemStyle: {
          color: '#1E9BBA',
        },
      },
      {
        name: '产出量缺口',
        type: 'bar',
        yAxisIndex: 1,
        itemStyle: {
          color: '#FF8C00',
        },
      },
    ],
  };
  const costUsageOp = {
    ...commonGrid,
    title: {
      text: '成本使用情况',
    },
    legend: getLegend([
      { name: '计划编码工量', icon: 'line' },
      { name: '编码使用工量', icon: 'line' },
    ]),
    xAxis: monthXAxis(false),
    yAxis: {
      name: '人月',
      type: 'value',
      splitLine,
    },
    series: [
      {
        name: '计划编码工量',
        type: 'line',
        smooth: true,
        // symbolSize: 6,
        showSymbol: false,
        // lineStyle: {
        //   width: 3,
        // },
        itemStyle: {
          color: '#2EF5C6',
        },
      },
      {
        name: '编码使用工量',
        type: 'line',
        smooth: true,
        showSymbol: false,
        itemStyle: {
          color: '#FF8C00',
        },
      },
    ],
  };
  const costAvailityOp = {
    title: {
      text: '各经营单元成本利用率',
    },
    legend: getLegend([
      { name: '成本利用率', icon: 'line' },
      { name: '成本超支量' },
    ]),
    xAxis: monthXAxis(true),
    yAxis: [
      {
        type: 'value',
        // name: '成本利用率',
        min: 40,
        max: 160,
        interval: 20,
        splitLine,
        axisLabel: {
          formatter: '{value}%',
        },
      },
      {
        type: 'value',
        // name: '成本超支',
        min: 0,
        max: 6,
        interval: 1,
        splitLine,
      },
    ],
    series: [
      {
        name: '成本利用率',
        type: 'line',
        showSymbol: false,
        smooth: true,
        itemStyle: {
          color: '#F4D322',
        },
      },
      {
        name: '成本超支量',
        type: 'bar',
        yAxisIndex: 1,
        itemStyle: {
          color: '#03C3FF',
        },
      },
    ],
  };

  //项目进度、成本分析
  const [progAndCost, setProgAndCost] = useState<{ paces: any; costs: any }>();
  useIntervalRequest(
    () => screenApis.projectPaceCostStatUsingGet({ projectYear: curYear }),
    data => {
      if (!data) {
        return;
      }
      if (data.paces) {
        data.paces.sort((a: any, b: any) =>
          (a.statMonth ?? '').localeCompare(b.statMonth ?? ''),
        );
        data.paces = [
          {
            data: data.paces.map((v: any) =>
              v.planCodingWorkLoad ? v.planCodingWorkLoad : undefined,
            ),
          }, //计划编码工量
          {
            data: data.paces.map((v: any) =>
              v.finishCodingWorkLoad ? v.finishCodingWorkLoad : undefined,
            ),
          }, //完成编码工量
        ];
      }
      if (data.costs) {
        data.costs.sort((a: any, b: any) =>
          (a.statMonth ?? '').localeCompare(b.statMonth ?? ''),
        );
        data.costs = [
          {
            data: data.costs.map((v: any) =>
              v.planCodingWorkLoad ? v.planCodingWorkLoad : undefined,
            ),
          }, //计划编码工量
          {
            data: data.costs.map((v: any) =>
              v.finishCodingWorkLoad ? v.finishCodingWorkLoad : undefined,
            ),
          }, //编码使用工量
        ];
      }
      setProgAndCost({
        paces: { series: data.paces },
        costs: { series: data.costs },
      });
    },
    { refreshToken: curYear },
  );

  // 经营单元进度符合度
  const [progCompliance, setProgCompliance] = useState<{
    tabData: any;
    data: any;
  }>();
  useIntervalRequest(
    () => screenApis.mgrUnitPaceUsingGet({ projectYear: curYear }),
    data => {
      if (!data) {
        return;
      }
      setProgCompliance({
        tabData: data.map((v: any) => v.managementUnitName),
        data: data.map((v: any) => {
          v.stats.sort((a: any, b: any) =>
            (a.statMonth ?? '').localeCompare(b.statMonth ?? ''),
          );
          return [
            {
              data: v.stats.map((v: any) =>
                v.paceRate ? v.paceRate : undefined,
              ),
            }, //进度符合度
            {
              data: v.stats.map((v: any) =>
                v.actualFinish < 0 ? 0 : v.actualFinish,
              ),
            }, //月完成情况
          ];
        }),
      });
    },
    { refreshToken: curYear },
  );

  // 经营单元成本利用率
  const [costAvaility, setCostAvaility] = useState<{
    tabData: any;
    data: any;
  }>();
  useIntervalRequest(
    () => screenApis.mgrUnitCostUsingGet({ projectYear: curYear }),
    data => {
      if (!data) {
        return;
      }
      setCostAvaility({
        tabData: data.map((v: any) => v.managementUnitName),
        data: data.map((v: any) => {
          v.stats.sort((a: any, b: any) =>
            (a.statMonth ?? '').localeCompare(b.statMonth ?? ''),
          );
          return [
            {
              data: v.stats.map((v: any) =>
                v.paceRate ? v.paceRate : undefined,
              ),
            }, //成本利用率
            {
              data: v.stats.map((v: any) =>
                v.actualFinish < 0 ? 0 : v.actualFinish,
              ),
            }, //成本超支
          ];
        }),
      });
    },
    { refreshToken: curYear },
  );

  //经营单元进度、成本风险
  const [progAndCostRisk, setProgAndCostRisk] = useState<{
    progs: any;
    cost: any;
  }>();
  useIntervalRequest(
    () => screenApis.mgrUnitRiskUsingGet({ projectYear: curYear }),
    data => {
      if (data) {
        setProgAndCostRisk({
          progs: data.map((v: any) => [
            v.managementName,
            v.projectCount,
            v.paceRiskCount,
            v.statMonth,
            v.produceGap,
          ]),
          cost: data.map((v: any) => [
            v.managementName,
            v.projectCount,
            v.costRiskCount,
            v.statMonth,
            myFixed(v.costRate, 0) + '%',
          ]),
        });
      }
    },
    { refreshToken: curYear },
  );

  return (
    <>
      <div className="h-full w-full grid grid-rows-2 gap-[24px]">
        <div className="flex flex-col">
          <div className="title title-long">项目进度分析</div>
          <div className="area grow grid grid-cols-3 gap-[16px] p-[16px]">
            <MyEchart
              option={progCmpleteOp}
              data={progAndCost?.paces}
              className="bloc-bg"
            />
            <MyScrollTabChart
              option={progComplianceOp}
              tabData={progCompliance?.tabData ?? []}
              data={progCompliance?.data ?? []}
              className="bloc-bg"
            />
            <MyScrollTable
              title={'各经营单元进度风险'}
              headData={[
                '经营单元',
                '项目数量',
                '进度风险数',
                '截止月份',
                '产出量缺口',
              ]}
              data={progAndCostRisk?.progs ?? []}
              className="bloc-bg"
            />
          </div>
        </div>

        <div className="flex flex-col">
          <div className="title title-long">项目成本分析</div>
          <div className="area grow grid grid-cols-3 gap-[16px] p-[16px]">
            <MyEchart
              option={costUsageOp}
              data={progAndCost?.costs}
              className="bloc-bg"
            />

            <MyScrollTabChart
              option={costAvailityOp}
              tabData={costAvaility?.tabData ?? []}
              data={costAvaility?.data ?? []}
              className="bloc-bg"
            />
            <MyScrollTable
              title={'各经营成本进度风险'}
              headData={[
                '经营单元',
                '项目数量',
                '成本风险数',
                '截止月份',
                '成本利用率',
              ]}
              data={progAndCostRisk?.cost ?? []}
              className="bloc-bg"
            />
          </div>
        </div>
      </div>
    </>
  );
};

//资源总览
const ResourcesView = () => {
  const [data, setData] = useState<StaffLargeScreenVO>({});

  const levelsOp = {
    title: {
      text: '前后端岗级对比',
    },
    legend: getLegend(),
    grid: [
      {
        left: '10',
        top: '40',
        right: '55%',
        bottom: '0',
      },
      {
        left: '56%',
        top: '40',
        right: '10',
        bottom: '0',
      },
    ],
    yAxis: [
      {
        gridIndex: 0,
        type: 'category',
        position: 'right',
        nameLocation: 'middle',
        axisLabel: {
          color: '#fff',
          fontWeight: 'bold',
        },
        axisTick: {
          show: false,
        },
        data: ['高', '中', '初'],
        axisLine: { show: false },
      },
      {
        show: false,
        gridIndex: 1,
        type: 'category',
        data: ['高', '中', '初'],
      },
    ],
    xAxis: [
      {
        show: false,
        gridIndex: 0,
        type: 'value',
        inverse: true,
      },
      {
        show: false,
        gridIndex: 1,
        type: 'value',
      },
    ],
    series: [
      {
        name: '前端',
        type: 'bar',
        xAxisIndex: 0,
        yAxisIndex: 0,
        itemStyle: { color: '#2EF5C6' },
        label: {
          show: true,
          position: 'insideLeft',
          color: '#333',
          fontWeight: 700,
        },
      },
      {
        name: '后端',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        itemStyle: { color: '#F4D322' },
        label: {
          show: true,
          position: 'insideRight',
          color: '#333',
          fontWeight: 700,
        },
      },
    ],
  };

  const dutyStatusOP = {
    title: {
      text: '每月人员在岗情况',
    },
    legend: getLegend(),
    grid: [
      {
        left: '10',
        top: '50',
        right: '10',
        bottom: '40%',
      },
      {
        left: '10',
        top: '79%',
        right: '10',
        bottom: '10',
      },
    ],
    xAxis: [
      {
        gridIndex: 0,
        ...monthXAxis(false),
        axisLine: { show: false },
      },
      {
        show: false,
        gridIndex: 1,
        ...monthXAxis(false),
      },
    ],
    yAxis: [
      {
        show: false,
        gridIndex: 0,
        type: 'value',
        splitLine: { show: false },
        axisLine: { lineStyle: { color: '#333' } },
      },
      {
        show: false,
        gridIndex: 1,
        type: 'value',
        inverse: true,
        splitLine: { show: false },
        axisLine: { lineStyle: { color: '#333' } },
      },
    ],
    series: [
      {
        name: '在岗',
        type: 'bar',
        xAxisIndex: 0,
        yAxisIndex: 0,
        itemStyle: { color: '#66D8FF' },
        label: {
          show: true,
          align: 'center',
          position: 'top',
          color: '#fff',
          fontSize: 8,
        },
      },
      {
        name: '不在岗',
        type: 'bar',
        xAxisIndex: 1,
        yAxisIndex: 1,
        itemStyle: { color: '#E6450F' },
        label: {
          show: true,
          align: 'center',
          position: 'bottom',
          color: '#fff',
          fontSize: 8,
        },
      },
    ],
  };

  useIntervalRequest(
    () => screenApis.staffOverviewUsingGet({ projectYear: curYear }),
    data => {
      if (!data) {
        return;
      }
      if (data.resOverview.jobs) {
        const js = data.resOverview.jobs;
        data.resOverview.jobs = [
          {
            data: [js[1].seniorCount, js[1].middleCount, js[1].primaryCount], //前端
          },
          {
            data: [js[0].seniorCount, js[0].middleCount, js[0].primaryCount], //后端
          },
        ];
      }
      if (data.workStates) {
        data.workStates.sort((a: any, b: any) =>
          (a.statMonth ?? '').localeCompare(b.statMonth ?? ''),
        );
        data.workStates = [
          {
            data: data.workStates.map((v: any) => v.dutyNumber), //在岗
          },
          {
            data: data.workStates.map((v: any) => v.notDutyNumber), //不在岗
          },
        ];
      }
      setData({ ...data });
    },
    { refreshToken: curYear },
  );

  const subInfo = (no?: number, unit?: string, desc?: string) => {
    return (
      <>
        <div className="flex items-center justify-center h-full">
          <div className="sub-info-desc justify-center ">{desc}</div>
          <div className="sub-info-text min-w-[60px]">
            {myFixed(no, 0)?.toString() ?? '-'}
          </div>
          <div className="sub-info-unit mt-[8px]">{unit}</div>
        </div>
      </>
    );
  };

  return (
    <div className="flex h-full w-full flex-col">
      <div className="title title-short">资源总览</div>
      <div className="area grow grid grid-cols-2 grid-rows-[45px_minmax(0,_1fr)] gap-[16px] p-[16px]">
        <div className="grid grid-cols-2 gap-y-[24px] bloc-bg col-span-2 !p-[0]">
          {subInfo(data.resOverview?.yearStaffTotal, '人', '年度可用人员')}
          {subInfo(data.resOverview?.yearWorkLoad, '人月', '年度可用工量')}
        </div>
        <MyEchart
          option={levelsOp}
          data={{ series: data.resOverview?.jobs }}
          className="bloc-bg"
        />
        <MyEchart
          option={dutyStatusOP}
          data={{ series: data.workStates }}
          className="bloc-bg"
        />
      </div>
    </div>
  );
};

//资源负载分析
const ResourcesAnalyzeView = () => {
  const loadOp = {
    ...commonGrid,
    title: {
      text: '资源负载度',
    },
    legend: getLegend([
      { name: '平均负载度', icon: 'dashed' },
      { name: '最大负载度', icon: 'line' },
    ]),
    xAxis: monthXAxis(false),
    yAxis: {
      type: 'value',
      min: 40,
      // max: 180,
      interval: 30,
      axisLabel: {
        formatter: '{value}%',
      },
      splitLine,
    },
    series: [
      {
        name: '平均负载度',
        type: 'line',
        smooth: true,
        showSymbol: false,
        lineStyle: {
          type: 'dashed',
        },
        itemStyle: {
          color: '#FFDF80',
        },
      },
      {
        name: '最大负载度',
        type: 'line',
        smooth: true,
        showSymbol: false,
        itemStyle: {
          color: '#FF8C00',
        },
      },
    ],
  };

  const usagesOp = {
    ...commonGrid,
    title: {
      text: '资源利用率',
    },
    xAxis: monthXAxis(false),
    yAxis: {
      type: 'value',
      min: 70,
      max: 100,
      interval: 5,
      axisLabel: {
        formatter: '{value}%',
      },
      splitLine,
    },
    series: [
      {
        type: 'line',
        showSymbol: false,
        itemStyle: {
          color: 'rgba(46, 245, 198, 1)',
          width: 3,
        },
        areaStyle: {
          color: 'rgba(46, 245, 198, 0.3)',
        },
      },
    ],
  };

  const [loadAndUsages, setLoadAndUsages] = useState<{
    load: any;
    usages: any;
  }>();
  useIntervalRequest(
    () => screenApis.staffLoadUsingGet({ projectYear: curYear }),
    data => {
      if (data) {
        data.sort((a: any, b: any) =>
          (a.statMonth ?? '').localeCompare(b.statMonth ?? ''),
        );
        setLoadAndUsages({
          load: {
            series: [
              { data: data.map((v: any) => v.avgLoad) },
              { data: data.map((v: any) => v.maxLoad) },
            ],
          },
          usages: { series: [{ data: data.map((v: any) => v.monthUsage) }] },
        });
      }
    },
    { refreshToken: curYear },
  );

  return (
    <div className="flex h-full w-full flex-col">
      <div className="title title-long">资源负载分析</div>
      <div className="area grow grid grid-cols-2 gap-[16px] p-[16px]">
        <MyEchart
          option={loadOp}
          data={loadAndUsages?.load ?? {}}
          className="bloc-bg"
        />
        <MyEchart
          option={usagesOp}
          data={loadAndUsages?.usages ?? {}}
          className="bloc-bg"
        />
      </div>
    </div>
  );
};

const YearSelect = ({ updater }: { updater: () => void }) => {
  return (
    <>
      <div className="flex">
        <div className="text-[16px] text-[rgba(255,255,255,0.4)] mr-[16px] content-center">
          统计年份
        </div>
        <div className="text-[rgba(255,255,255,0.4)] mr-[16px] content-center">
          |
        </div>
        <MyDropdown
          options={
            allDist['large_screen_q_year']?.map((v: any) => v.dictValue) ?? []
          }
          defaultV={curYear}
          callback={year => {
            curYear = year;
            updater();
          }}
        />
      </div>
    </>
  );
};

const TimeArea = () => {
  const [times, setTimes] = useState<string>('');
  useInterval(
    () => {
      const now = new Date();
      const formattedDate = now.toLocaleDateString('zh-CN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      });
      const formattedTime = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
      });
      setTimes(
        `${formattedDate} ${formattedTime} ${now.toLocaleDateString('zh-CN', {
          weekday: 'long',
        })}`,
      );
    },
    1000,
    { immediate: true },
  );
  return (
    <>
      <div className="text-[#FCFCFC] grid grid-cols-[auto_auto] p-[16px] grid-rows-2 gap-x-[16px] mr-[20px]">
        <div className="row-span-2 text-[40px] w-[157px] times">
          {times.split(' ')[1]}
        </div>
        <div className="text-[10px] content-end">{times.split(' ')[2]}</div>
        <div className="text-[14px] opacity-50">{times.split(' ')[0]}</div>
      </div>
    </>
  );
};

const DataScreen = () => {
  const [refreshKey, setRefreshKey] = useState(0);
  const refresh = () => {
    setRefreshKey(Math.random());
  };
  useEffect(() => {
    getBaseResult(screenApis.getAllUsingGet2()).then((dist: any) => {
      allDist = dist;
      refresh();
    });
    const handleResize = () => {
      refresh();
    };
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <>
      <div
        id="page"
        className="flex flex-col h-screen w-screen"
        key={refreshKey}
      >
        <div className="header flex items-center justify-end">
          <YearSelect updater={refresh} />
          <TimeArea />
        </div>
        <div className="grow grid grid-cols-3 grid-rows-3 gap-[24px] p-[24px]">
          <div className="col-span-1 row-span-2">{ProjectsView()}</div>
          <div className="col-span-2 row-span-2">{ProjectsAnalyzeView()}</div>
          <div className="col-span-1 row-span-1">{ResourcesView()}</div>
          <div className="col-span-2 row-span-1">{ResourcesAnalyzeView()}</div>
        </div>
      </div>
    </>
  );
};

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <DataScreen />,
);

// export default DataScreen;
