// import { useEffect, useState } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';

// import { getBaseResult, ucApis } from '@/api';
// import { getRouteByPath } from '@/config/router';
import {
  LogoutOutlined,
  SwapOutlined,
  // SmileOutlined
} from '@ant-design/icons';
import { ProLayout } from '@ant-design/pro-components';
import { logout } from '@/api/axiosConfig';
// import { useUserInfoStore } from '@/store/userinfo';
// import { UserCenterTenantVO } from '@/api/servies/ucApi';
import {
  Button,
  Dropdown,
  // Tabs
} from 'antd';
import { PROJECT_TITLE } from '@/utils';

import { useGetUserInfo, useSystemMenu } from '@/api/querys/common';

import { RouteAuthorization } from '@hbwow/components';

// const IconMap = {
//   smile: <SmileOutlined />,
// };

//menu格式适配
// const getMenu = (node: any) => {
//   return (
//     node?.children
//       ?.filter((m: any) => m.type == 2 || m.type == undefined)
//       ?.map((m: any) => {
//         return {
//           icon: m.icon && IconMap[m.icon as 'smile'],
//           path: m.routeUrl ?? m.path,
//           name: m.name,
//           routes: getMenu(m),
//           children: getMenu(m),
//           key: m.id,
//         };
//       }) ?? []
//   );
// };

// const getMenuByPath = (path: string, ms?: any): any => {
//   const temps = ms ?? [];
//   for (let i = 0; i < temps.length; i++) {
//     const m = temps[i];
//     if (m.path == path) {
//       return m;
//     }
//     const r: any = getMenuByPath(path, m.children ?? []);
//     if (r != undefined) {
//       return r;
//     }
//   }
// };

const Layout = () => {
  // const userInfo: UserCenterTenantVO = useUserInfoStore(s => s.userInfo);
  const { data: userInfo = {} } = useGetUserInfo();
  const { data: dataSystemMenu, isLoading: isLoadingMenu } = useSystemMenu();
  const navigate = useNavigate();
  // const [menu, setMenu] = useState<Array<any>>();
  // const [tabList, setTabList] = useState<
  //   Array<{
  //     label: string;
  //     key: string;
  //     path: string;
  //     children: any;
  //     closable: boolean;
  //   }>
  // >([]);
  // const [curTab, setCurTab] = useState<any>({});
  // const initMenu = () => {
  //   // setMenu(getMenu(allRoutes[0]));
  //   // setMenu(getMenu(getDataFromLocalStorage('menu')));
  //   menu ??
  //     getBaseResult(
  //       ucApis.menuUsingGet({ subSystemCode: 'WEB-00001' }),
  //       '获取菜单失败',
  //     )
  //       .then(m => {
  //         setMenu(getMenu(m.menu?.[0]));
  //       })
  //       .catch(() => setMenu([]));
  // };
  // useEffect(() => {
  //   initMenu();
  // }, []);
  //监听路由变化，实现全局打开tab功能
  const location = useLocation();
  const navi = useNavigate();
  // const createTab = (menu: any) => {
  //   let tab = tabList.find(t => t.key == menu.key);
  //   if (!tab) {
  //     tab = {
  //       label: menu.name,
  //       key: menu.key,
  //       path: menu.path,
  //       closable: true,
  //       children: getRouteByPath(menu.path)?.element,
  //     };
  //     setTabList([...tabList, tab]);
  //   }
  //   setCurTab(tab);
  // };
  // useEffect(() => {
  //   // console.log('location:', location.pathname);
  //   if (location.pathname == '/') {
  //     return;
  //   }
  //   const m = getMenuByPath(location.pathname, menu);
  //   if (m) {
  //     createTab(m);
  //   }
  // }, [location, menu]);

  // const onTabEdit = (key: any, action: string) => {
  //   if (action == 'remove') {
  //     const newTabList = tabList.filter(t => t.key != key);
  //     setTabList(newTabList);
  //     if (newTabList.length > 0) {
  //       navi(newTabList[newTabList.length - 1].path);
  //     }
  //   }
  // };

  return (
    <div className='bg-blue-800 h-screen'>
      <ProLayout
        title={PROJECT_TITLE}
        logo={false}
        layout='mix'
        fixSiderbar={true}
        avatarProps={{
          title: userInfo?.user?.name,
          render: (props, dom) => {
            return (
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'switchSystem',
                      icon: <SwapOutlined />,
                      label: '切换系统',
                      onClick: () => {
                        window.location.href = '/login/list';
                      },
                    },
                    {
                      key: 'logout',
                      icon: <LogoutOutlined />,
                      label: '退出登录',
                      onClick: () => logout(),
                    },
                  ],
                }}
              >
                {dom}
              </Dropdown>
            );
          },
        }}
        menuItemRender={(item, dom) => <a onClick={() => navi(item.path ?? '')}>{dom}</a>}
        loading={isLoadingMenu}
        route={dataSystemMenu?.route}
        // selectedKeys={[curTab.key]}
        token={{
          header: {
            colorBgHeader: '#1890ff',
            colorHeaderTitle: '#fff',
            colorTextRightActionsItem: '#fff',
          },
        }}
        location={location}
        menu={{ autoClose: false, loading: isLoadingMenu }}
        onMenuHeaderClick={() => {
          navigate('/');
        }}
      >
        {/* <Tabs
          type="editable-card"
          hideAdd={true}
          onChange={key => {
            const path = tabList.find(t => t.key == key)?.path;
            if (path) {
              navi(path);
            }
          }}
          activeKey={curTab.key}
          onEdit={(e, action) => onTabEdit(e, action)}
          items={tabList}
        /> */}

        <RouteAuthorization
          route={dataSystemMenu?.route}
          pathname={location.pathname}
          resultProps={{
            extra: (
              <Button
                type='primary'
                onClick={() => {
                  navigate('/');
                }}
              >
                回到首页
              </Button>
            ),
          }}
        >
          <Outlet />
        </RouteAuthorization>
      </ProLayout>
    </div>
  );
};
export default Layout;
