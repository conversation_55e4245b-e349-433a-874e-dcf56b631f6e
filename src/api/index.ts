import { Api as commonApi } from './servies/commonApi';
export const commonApis = new commonApi().pmt;
import { Api as plmApi } from './servies/plmApi';
export const plmApis = new plmApi().pmt;
import { Api as ucApi } from './servies/ucApi';
export const ucApis = new ucApi().pmt;

// ------ 未整理 swaggerUI 接口 ------
import { Api as screenApi } from './servies/screenApi';
export const screenApis = new screenApi().pmt;
import { Api as plmbaseApi } from './servies/plmBase';
export const plmbaseApis = new plmbaseApi().pmt;
// ------ 未整理 swaggerUI 接口 ------

import { message } from 'antd';

export const getBaseResult = async (request: Promise<any>, errTip?: string): Promise<any> => {
  return request
    .then((r) => {
      if (r && r.data?.code === 200) {
        return r?.data?.data;
      } else {
        throw new Error(r?.data?.message ?? '');
      }
    })
    .catch((e: any) => {
      if (errTip) {
        message.error(`${errTip}:${e.message}`);
      }
      // console.log(e.message);
    });
};

export const getPageResult = async (request: Promise<any>, errTip?: string): Promise<any> => {
  return request
    .then((r) => {
      if (r && r.data?.code === 200) {
        return {
          data: r?.data?.data,
          success: r?.data?.data,
          total: r?.data?.totalRecords,
        };
      } else {
        throw new Error(r?.data?.message ?? '');
      }
    })
    .catch((e: any) => {
      message.error(errTip + ':' + e.message);
    });
};
