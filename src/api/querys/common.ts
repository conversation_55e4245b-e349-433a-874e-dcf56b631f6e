import { useMutation, useQuery } from '@tanstack/react-query';
import { md } from '@hbwow/utils';

import { getBaseResult, commonApis, ucApis } from '..';
import { INFINITY_QUERY_CONFIG } from '@/utils';
import { routes as devEnvUseLocalRoutes } from '@/config/router';
import { handleDownload } from '@hbwow/hooks';

const { formatTreeData } = md;

// 用户信息
export const useGetUserInfo = () => {
  return useQuery({
    queryKey: ['ucApis.ucUserCenterGetUserInfoList'],
    queryFn: async () => {
      const data = await getBaseResult(
        ucApis.ucUserCenterGetUserInfoList(),
        '获取用户数据失败，请检查网络！',
      );

      return data;
    },
    ...INFINITY_QUERY_CONFIG,
  });
};

// 系统菜单
export const useSystemMenu = () => {
  const SUB_SYSTEM_CODE = 'WEB-00001';

  return useQuery({
    queryKey: ['ucApis.ucAuthMenuList'],
    queryFn: async (): Promise<{
      menu: any[];
      perms: any[];
      route: Record<any, any>;
    }> => {
      const data = await getBaseResult(
        ucApis.ucAuthMenuList({ subSystemCode: SUB_SYSTEM_CODE }),
        '获取菜单失败',
      );

      const { menu = [], perms = {} } = data;

      // const _route = { children: formatTreeData(menu[0]?.children) };
      // const _perms = [];

      const _route = import.meta.env.DEV
        ? devEnvUseLocalRoutes[0]
        : { children: formatTreeData(menu[0]?.children) };
      const _perms = perms[SUB_SYSTEM_CODE];

      return {
        menu: menu,
        perms: _perms,
        route: _route,
      };
    },
    ...INFINITY_QUERY_CONFIG,
  });
};

const need2Number = ['plm_project_state', 'plm_risk_state', 'plm_risk_type'];
// 字典表
export const useDictData = () => {
  return useQuery({
    ...INFINITY_QUERY_CONFIG,
    queryKey: ['commonApis.commonDictGetAllList'],
    queryFn: async (): Promise<any> => {
      const { data } = await commonApis.commonDictGetAllList();

      const formatData = Object.entries(data?.data || {}).reduce<Record<string, any[]>>(
        (acc, [key, value]) => {
          if (need2Number.includes(key)) {
            acc[key] = (value as any[]).map((item: any) => ({
              ...item,
              dictValue: Number(item.dictValue),
            }));
          } else {
            acc[key] = value as any[];
          }

          return acc;
        },
        {},
      );

      const custData = {
        '0-1': [
          { dictLabel: '否', dictValue: 0 },
          { dictLabel: '是', dictValue: 1 },
        ],
      };

      return { ...formatData, ...custData };
    },
  });
};

// 字典表选项
export const useDictOptions = () => {
  const { data = {}, ...rest } = useDictData();

  const _data: Record<string, any[]> = Object.entries(data as Record<string, any[]>).reduce<
    Record<string, any[]>
  >((acc, [key, value]) => {
    acc[key] = (value as any[]).map((item: any) => ({
      label: item.dictLabel,
      value: item.dictValue,
    }));

    return acc;
  }, {});

  return { data: _data, ...rest };
};

// 数据字典回显以及 label 取值
export const useDictMap = () => {
  const { data = {}, ...rest } = useDictData();

  const getValueByLabel = Object.entries(data as Record<string, any[]>).reduce<Record<string, any>>(
    (acc, [key, value]) => {
      acc[key] = value.reduce((_acc, item: any) => {
        _acc[item.dictLabel] = item.dictValue;

        return _acc;
      }, {});

      return acc;
    },
    {},
  );

  const getLabelByValue = Object.entries(data as Record<string, any[]>).reduce<Record<string, any>>(
    (acc, [key, value]) => {
      acc[key] = value.reduce((_acc, item: any) => {
        _acc[item.dictValue] = item.dictLabel;

        return _acc;
      }, {});

      return acc;
    },
    {},
  );

  return { data: { getValueByLabel, getLabelByValue }, ...rest };
};

// 下载文件 useMutation
export const useDownload = () => {
  return useMutation({
    mutationFn: handleDownload,
  });
};
