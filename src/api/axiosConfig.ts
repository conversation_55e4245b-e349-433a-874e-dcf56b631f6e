import { getDataFromLocalStorage, getQueryVariable, saveDataToLocalStorage } from '@/utils';
import devEnvOneClickLogin from '@/utils/devEnvOneClickLogin';
import { message } from 'antd';
import { AxiosResponse, AxiosError } from 'axios';

const tokenKey = 'authorization';

export const saveTokenFormUrl = () => {
  const token = getQueryVariable('token');
  if (token !== '') {
    saveDataToLocalStorage(tokenKey, token);
  }
};

// TODO logout
export const logout = () => {
  localStorage.removeItem(tokenKey);
  // window.location.href = '/login';
};

export const getHeader = () => {
  return {
    Authorization: `Bearer ${getDataFromLocalStorage(tokenKey)}`,
  };
};

/** 设置请求拦截 **/
const requestConfig = (config: any) => {
  config.headers = { ...getHeader(), ...config.headers };
  return config;
};
const responseConfig = (response: AxiosResponse) => {
  if (
    response.status === 401 ||
    response.data?.code === 40110 ||
    response.data?.code === 40111 ||
    response.data?.code === 40116
  ) {
    logout();
  }

  if (response.data?.code !== 200) {
    message.error(response.data?.message);
    return Promise.reject(response);
  }

  return response;
};
const errorConfig = (error: AxiosError) => {
  const code: any = (error.response?.data as any)['code'] ?? '';

  if (error.response?.status === 401 || code === 40110 || code === 40111 || code === 40116) {
    devEnvOneClickLogin();

    logout();
  }
  return Promise.reject(error);
};

export { requestConfig, responseConfig, errorConfig };
