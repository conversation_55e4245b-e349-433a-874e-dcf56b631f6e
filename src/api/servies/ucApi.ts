/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/**
 * AuthorityGroupAddDTO对象
 * 权限组新增对象
 */
export interface AuthorityGroupAddDTO对象 {
  /** 权限组名称 */
  authorityGroupName?: string;
  /** 备注描述 */
  remark?: string;
  /** 权限id集合 */
  resourceIdList?: number[];
  /**
   * 状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /** 管理员id集合 */
  userIdList?: number[];
}

/**
 * AuthorityGroupCheckBoxVO对象
 * 权限组复选框对象
 */
export interface AuthorityGroupCheckBoxVO对象 {
  /** 权限组名称 */
  authorityGroupName?: string;
  /**
   * 是否选中
   * @example false
   */
  flag?: boolean;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 备注描述 */
  remark?: string;
  /**
   * 状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * AuthorityGroupChgStatusDTO对象
 * 批量修改权限组状态的对象
 */
export interface AuthorityGroupChgStatusDTO对象 {
  /** 权限组ID集合 */
  groupIdList: number[];
  /**
   * 状态值,1-启用或0-停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status: 0 | 1 | 2 | 3 | 4;
}

/**
 * AuthorityGroupDetailVO对象
 * 权限组详细信息
 */
export interface AuthorityGroupDetailVO对象 {
  /** 权限组名称 */
  authorityGroupName?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 管理员列表 */
  managerList?: AuthorityGroupManagerVO对象[];
  /** 备注描述 */
  remark?: string;
  /** 角色列表 */
  roleList?: RoleAuthorityGroupVO对象[];
  /**
   * 状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * AuthorityGroupManagerVO对象
 * 权限组已分配的管理员对象
 */
export interface AuthorityGroupManagerVO对象 {
  /** 对应人员姓名 */
  employeeName?: string;
  /**
   * 用户id
   * @format int64
   */
  id?: number;
  /** 用户名 */
  name?: string;
}

/**
 * AuthorityGroupPageDetailVO对象
 * 权限组分页列表信息
 */
export interface AuthorityGroupPageDetailVO对象 {
  /**
   * 管理员数量
   * @format int32
   */
  adminCount?: number;
  /** 权限组名称 */
  authorityGroupName?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 备注描述 */
  remark?: string;
  /**
   * 角色数量
   * @format int32
   */
  roleCount?: number;
  /**
   * 状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * AuthorityGroupPagedDTO分页对象
 * 权限组信息
 */
export interface AuthorityGroupPagedDTO分页对象 {
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 权限组名称，支持模糊查询 */
  name?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /**
   * 权限组状态【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * AuthorityGroupUpdateDTO对象
 * 权限组更新对象
 */
export interface AuthorityGroupUpdateDTO对象 {
  /** 权限组名称 */
  authorityGroupName?: string;
  /**
   * 权限组id
   * @format int64
   */
  id?: number;
  /** 备注描述 */
  remark?: string;
  /** 权限id集合 */
  resourceIdList?: number[];
  /**
   * 状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /** 管理员id集合 */
  userIdList?: number[];
}

/**
 * AuthorityGroupUpdateManagerDTO对象
 * 权限组关联管理员对象
 */
export interface AuthorityGroupUpdateManagerDTO对象 {
  /**
   * 权限组id
   * @format int64
   */
  id?: number;
  /** 管理员id集合 */
  userIdList?: number[];
}

/**
 * AuthorityGroupVO对象
 * 权限组信息
 */
export interface AuthorityGroupVO对象 {
  /** 权限组名称 */
  authorityGroupName?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 备注描述 */
  remark?: string;
  /**
   * 状态，0-停用，1-启用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/** BaseResultOfAuthorityGroupDetailVO对象 */
export interface BaseResultOfAuthorityGroupDetailVO对象 {
  /** @format int32 */
  code?: number;
  /** 权限组详细信息 */
  data?: AuthorityGroupDetailVO对象;
  message?: string;
}

/** BaseResultOfEmployeeVO对象 */
export interface BaseResultOfEmployeeVO对象 {
  /** @format int32 */
  code?: number;
  /** 系统所管理的人员 */
  data?: EmployeeVO对象;
  message?: string;
}

/** BaseResultOfFileAttachment对象 */
export interface BaseResultOfFileAttachment对象 {
  /** @format int32 */
  code?: number;
  /** 文件记录表实体类 */
  data?: FileAttachment对象;
  message?: string;
}

/** BaseResultOfJobPositionVO对象 */
export interface BaseResultOfJobPositionVO对象 {
  /** @format int32 */
  code?: number;
  /** 岗位信息表 */
  data?: JobPositionVO对象;
  message?: string;
}

/** BaseResultOfJobPosition对象 */
export interface BaseResultOfJobPosition对象 {
  /** @format int32 */
  code?: number;
  /** 岗位信息表 */
  data?: JobPosition对象;
  message?: string;
}

/** BaseResultOfListOfAuthorityGroupCheckBoxVO对象 */
export interface BaseResultOfListOfAuthorityGroupCheckBoxVO对象 {
  /** @format int32 */
  code?: number;
  data?: AuthorityGroupCheckBoxVO对象[];
  message?: string;
}

/** BaseResultOfListOfAuthorityGroupManagerVO对象 */
export interface BaseResultOfListOfAuthorityGroupManagerVO对象 {
  /** @format int32 */
  code?: number;
  data?: AuthorityGroupManagerVO对象[];
  message?: string;
}

/** BaseResultOfListOfAuthorityGroupVO对象 */
export interface BaseResultOfListOfAuthorityGroupVO对象 {
  /** @format int32 */
  code?: number;
  data?: AuthorityGroupVO对象[];
  message?: string;
}

/** BaseResultOfListOfEmployeeVO对象 */
export interface BaseResultOfListOfEmployeeVO对象 {
  /** @format int32 */
  code?: number;
  data?: EmployeeVO对象[];
  message?: string;
}

/** BaseResultOfListOfFileAttachment对象 */
export interface BaseResultOfListOfFileAttachment对象 {
  /** @format int32 */
  code?: number;
  data?: FileAttachment对象[];
  message?: string;
}

/** BaseResultOfListOfJobPositionComboVO对象 */
export interface BaseResultOfListOfJobPositionComboVO对象 {
  /** @format int32 */
  code?: number;
  data?: JobPositionComboVO对象[];
  message?: string;
}

/** BaseResultOfListOfJobPositionVO对象 */
export interface BaseResultOfListOfJobPositionVO对象 {
  /** @format int32 */
  code?: number;
  data?: JobPositionVO对象[];
  message?: string;
}

/** BaseResultOfListOfOrganization对象 */
export interface BaseResultOfListOfOrganization对象 {
  /** @format int32 */
  code?: number;
  data?: Organization对象[];
  message?: string;
}

/** BaseResultOfListOfRoleAuthorizedUserVO对象 */
export interface BaseResultOfListOfRoleAuthorizedUserVO对象 {
  /** @format int32 */
  code?: number;
  data?: RoleAuthorizedUserVO对象[];
  message?: string;
}

/** BaseResultOfListOfRoleDetailVO对象 */
export interface BaseResultOfListOfRoleDetailVO对象 {
  /** @format int32 */
  code?: number;
  data?: RoleDetailVO对象[];
  message?: string;
}

/** BaseResultOfListOfRoleVO对象 */
export interface BaseResultOfListOfRoleVO对象 {
  /** @format int32 */
  code?: number;
  data?: RoleVO对象[];
  message?: string;
}

/** BaseResultOfListOfUserAllVO用户完整信息 */
export interface BaseResultOfListOfUserAllVO用户完整信息 {
  /** @format int32 */
  code?: number;
  data?: UserAllVO用户完整信息[];
  message?: string;
}

/** BaseResultOfListOfUserEmployeeVO用户详细信息VO */
export interface BaseResultOfListOfUserEmployeeVO用户详细信息VO {
  /** @format int32 */
  code?: number;
  data?: UserEmployeeVO用户详细信息VO[];
  message?: string;
}

/** BaseResultOfListOfUserVO对象 */
export interface BaseResultOfListOfUserVO对象 {
  /** @format int32 */
  code?: number;
  data?: UserVO对象[];
  message?: string;
}

/** BaseResultOfLoginUserDetails */
export interface BaseResultOfLoginUserDetails {
  /** @format int32 */
  code?: number;
  data?: LoginUserDetails;
  message?: string;
}

/** BaseResultOfMapOfstringAndobject */
export interface BaseResultOfMapOfstringAndobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** BaseResultOfMapOfstringAnd用户人员ID对象 */
export interface BaseResultOfMapOfstringAnd用户人员ID对象 {
  /** @format int32 */
  code?: number;
  data?: Record<string, Type用户人员ID对象>;
  message?: string;
}

/** BaseResultOfOrganizationVO对象 */
export interface BaseResultOfOrganizationVO对象 {
  /** @format int32 */
  code?: number;
  /** 组织机构，包括根、公司、部门等等 */
  data?: OrganizationVO对象;
  message?: string;
}

/** BaseResultOfRoleDetailVO对象 */
export interface BaseResultOfRoleDetailVO对象 {
  /** @format int32 */
  code?: number;
  /** 角色详情展示数据对象 */
  data?: RoleDetailVO对象;
  message?: string;
}

/** BaseResultOfRoleUserVO对象 */
export interface BaseResultOfRoleUserVO对象 {
  /** @format int32 */
  code?: number;
  /** 编辑用户时使用的角色列表，分为未授权角色集和已授权角色集 */
  data?: RoleUserVO对象;
  message?: string;
}

/** BaseResultOfRole对象 */
export interface BaseResultOfRole对象 {
  /** @format int32 */
  code?: number;
  /** 用于授权的角色 */
  data?: Role对象;
  message?: string;
}

/** BaseResultOfUserCenterTenantVO */
export interface BaseResultOfUserCenterTenantVO {
  /** @format int32 */
  code?: number;
  /** 用户中心租户admin账号信息 */
  data?: UserCenterTenantVO;
  message?: string;
}

/** BaseResultOfUserDetailVO用户详细信息VO */
export interface BaseResultOfUserDetailVO用户详细信息VO {
  /** @format int32 */
  code?: number;
  data?: UserDetailVO用户详细信息VO;
  message?: string;
}

/** BaseResultOfUserEnterpriseAdminVO企业管理员VO */
export interface BaseResultOfUserEnterpriseAdminVO企业管理员VO {
  /** @format int32 */
  code?: number;
  data?: UserEnterpriseAdminVO企业管理员VO;
  message?: string;
}

/** BaseResultOfUserVO对象 */
export interface BaseResultOfUserVO对象 {
  /** @format int32 */
  code?: number;
  /** 登录并使用系统的用户 */
  data?: UserVO对象;
  message?: string;
}

/** BaseResultOfboolean */
export interface BaseResultOfboolean {
  /** @format int32 */
  code?: number;
  data?: boolean;
  message?: string;
}

/** BaseResultOfobject */
export interface BaseResultOfobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** BaseResultOfstring */
export interface BaseResultOfstring {
  /** @format int32 */
  code?: number;
  data?: string;
  message?: string;
}

/**
 * EmployeeDTO对象
 * 系统所管理的人员
 */
export interface EmployeeDTO对象 {
  /**
   * 所属机构的id
   * @format int64
   */
  belongToOrg: number;
  /**
   * 人员的出生日期
   * @format date
   */
  birthday?: string;
  /** 钉钉账号 */
  dingtalk?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 员工的编号 */
  employeeCode?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender: number;
  /** 人员的毕业院校 */
  graduateFrom?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /** 人员的岗位 */
  jobPosition?: string;
  /** 员工的职务 */
  jobTitle?: string;
  /** 人员的姓名 */
  name: string;
  /** 人员的电话 */
  phone?: string;
  /** 人员头像的地址 */
  photoRul?: string;
  /** 人员的政治面貌 */
  politicalStatus?: string;
  /**
   * 人员当前的状态，例如在职、离职、停职等
   * @format int32
   */
  status?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 工作航站(多个逗号分隔)
   * @example "CTU,PEK"
   */
  workTerminal?: string;
  /** 微信OpenId */
  wxOpenid?: string;
}

/**
 * EmployeeOrgDTO对象
 * 批量修改人员所属机构的数据传输对象
 */
export interface EmployeeOrgDTO对象 {
  /** 所有需要修改机构的人员ID */
  employeeIds: number[];
  /**
   * 目标机构ID
   * @format int64
   */
  orgId: number;
}

/**
 * EmployeePagedDTO分页对象
 * 系统所管理的人员
 */
export type EmployeePagedDTO分页对象 = object;

/** EmployeeUserDTO */
export interface EmployeeUserDTO {
  /**
   * 系统生成用户名
   * @example false
   */
  autoUserName?: boolean;
  /** 人员信息对象 */
  employee?: EmployeeDTO对象;
  /**
   * 是否创建用户
   * @example false
   */
  isCreateUser?: boolean;
  /** 用户信息对象 */
  user?: UserAddDTO新增DTO对象;
}

/**
 * EmployeeVO对象
 * 系统所管理的人员
 */
export interface EmployeeVO对象 {
  /**
   * 所属机构的id
   * @format int64
   */
  belongToOrg: number;
  /**
   * 人员的出生日期
   * @format date
   */
  birthday?: string;
  /** 钉钉账号 */
  dingtalk?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 员工的编号 */
  employeeCode?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender: number;
  /** 人员的毕业院校 */
  graduateFrom?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /** 人员的岗位 */
  jobPosition?: string;
  /** 员工的职务 */
  jobTitle?: string;
  /** 人员的姓名 */
  name: string;
  /** 所属机构名称 */
  organizationName?: string;
  /** 人员的电话 */
  phone?: string;
  /** 人员头像的地址 */
  photoRul?: string;
  /** 人员的政治面貌 */
  politicalStatus?: string;
  /** 岗位名称 */
  positionName?: string;
  /** 岗位名称集合 */
  positionNameList?: string[];
  /**
   * 人员当前的状态，例如在职、离职、停职等
   * @format int32
   */
  status?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 工作航站(多个逗号分隔)
   * @example "CTU,PEK"
   */
  workTerminal?: string;
  /** 工作场站名称 */
  workTerminalList?: string[];
  /** 微信OpenId */
  wxOpenid?: string;
}

/**
 * FeatureResourceDeleteDTO
 * 系统功能资源删除参数接收对象，包括资源ID数组
 */
export interface FeatureResourceDeleteDTO {
  /** 权限组ID集合 */
  idList: number[];
}

/**
 * FileAttachment对象
 * 文件记录表实体类
 */
export interface FileAttachment对象 {
  /** 使用minio时文件存储的桶名称 */
  bucketName?: string;
  /**
   * 归属业务ID
   * @format int64
   */
  businessId?: number;
  /** 归属业务类型 */
  businessType?: string;
  /** 文件编号 */
  fileCode?: string;
  /** 文件扩展名 */
  fileExtension?: string;
  /** 文件信息 */
  fileMeta?: string;
  /** 文件名 */
  fileName?: string;
  /** 文件路径 */
  filePath?: string;
  /**
   * 文件大小
   * @format int64
   */
  fileSize?: number;
  /** 文件类型 */
  fileType?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 文件原名 */
  originalName?: string;
  /** 备注 */
  remarks?: string;
}

/**
 * JobPositionComboVO对象
 * 岗位下拉选择框对象
 */
export interface JobPositionComboVO对象 {
  /** 岗位集合 */
  jobPositionVOList?: JobPositionVO对象[];
  /**
   * 岗位类型值
   * @format int32
   */
  jobType?: number;
  /** 岗位类型名称 */
  jobTypeName?: string;
}

/**
 * JobPositionDTO对象
 * 岗位信息表
 */
export interface JobPositionDTO对象 {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 岗位代码 */
  positionCode?: string;
  /** 岗位名称 */
  positionName?: string;
  /**
   * 岗位类型
   * @format int32
   */
  positionType?: number;
  /** 岗位描述 */
  remark?: string;
}

/**
 * JobPositionDeleteDTO对象
 * 岗位删除对象
 */
export interface JobPositionDeleteDTO对象 {
  /** id集合 */
  idList?: number[];
}

/**
 * JobPositionPagedDTO分页对象
 * 岗位信息表
 */
export interface JobPositionPagedDTO分页对象 {
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 岗位代码 */
  positionCode?: string;
  /** 岗位名称 */
  positionName?: string;
  /**
   * 岗位类型
   * @format int32
   */
  positionType?: number;
}

/**
 * JobPositionVO对象
 * 岗位信息表
 */
export interface JobPositionVO对象 {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 岗位代码 */
  positionCode?: string;
  /** 岗位名称 */
  positionName?: string;
  /**
   * 岗位类型
   * @format int32
   */
  positionType?: number;
  /** 岗位描述 */
  remark?: string;
}

/**
 * JobPosition对象
 * 岗位信息表
 */
export interface JobPosition对象 {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 岗位代码 */
  positionCode?: string;
  /** 岗位名称 */
  positionName?: string;
  /**
   * 岗位类型
   * @format int32
   */
  positionType?: number;
  /** 岗位描述 */
  remark?: string;
}

/**
 * LabelVO对象
 * 系统中可用的标签项
 */
export interface LabelVO对象 {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 标签所有选项 */
  labelValues?: LabelValueCandidateVO对象[];
  /** 标签的名称 */
  name?: string;
  /**
   * 用户是否有该标签
   * @example false
   */
  selected?: boolean;
  /**
   * 标签项的类型，是单选标签还是多选标签
   * @format int32
   */
  type?: number;
}

/**
 * LabelValueCandidateVO对象
 * 系统中标签的可选值
 */
export interface LabelValueCandidateVO对象 {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 标签值的显示名称 */
  name?: string;
  /**
   * 用户是否有该标签值
   * @example false
   */
  selected?: boolean;
  /** 可选值 */
  value?: string;
}

/** LoginUserDetails */
export type LoginUserDetails = object;

/** OrderItem */
export interface OrderItem {
  asc?: boolean;
  column?: string;
}

/**
 * OrganizationDTO对象
 * 组织机构，包括根、公司、部门等等
 */
export interface OrganizationDTO对象 {
  /**
   * 机构的地址
   * @minLength 0
   * @maxLength 32
   */
  address?: string;
  /**
   * 机构所从事的业务的类别
   * @format int64
   */
  businessType?: number;
  /**
   * 机构的编码，例如PSC
   * @minLength 0
   * @maxLength 8
   */
  code?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 机构所属的行业
   * @format int64
   */
  industryType?: number;
  /**
   * 机构的简介
   * @minLength 0
   * @maxLength 65
   */
  introduction?: string;
  /**
   * 组织机构的全称
   * @minLength 0
   * @maxLength 21
   */
  name?: string;
  /**
   * 机构的英文名称
   * @minLength 0
   * @maxLength 64
   */
  nameEn?: string;
  /** name全路径 */
  nameFullPath?: string;
  /**
   * 父机构ID
   * @format int64
   */
  parentId?: number;
  /**
   * 机构的负责人
   * @minLength 0
   * @maxLength 20
   */
  personInCharge?: string;
  /**
   * 机构的电话
   * @minLength 0
   * @maxLength 20
   */
  phone?: string;
  /**
   * 机构的代码，由代码生成
   * @minLength 0
   * @maxLength 32
   */
  serialNo?: string;
  /**
   * 机构名称的简称
   * @minLength 0
   * @maxLength 16
   */
  shortName?: string;
  /**
   * 排序号
   * @format int32
   */
  sortOrder?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/**
 * OrganizationPagedDTO分页对象
 * 机构分页对象
 */
export interface OrganizationPagedDTO分页对象 {
  /** 机构的编码，例如PSC */
  code?: string;
  /**
   * 组织ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 组织机构的全称 */
  name?: string;
  /** 机构的英文名称 */
  nameEn?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 机构名称的简称 */
  shortName?: string;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/**
 * OrganizationVO对象
 * 组织机构，包括根、公司、部门等等
 */
export interface OrganizationVO对象 {
  /**
   * 机构的地址
   * @minLength 0
   * @maxLength 32
   */
  address?: string;
  /**
   * 机构所从事的业务的类别
   * @format int64
   */
  businessType?: number;
  /** 机构子节点 */
  child?: OrganizationVO对象[];
  /**
   * 机构的编码，例如PSC
   * @minLength 0
   * @maxLength 8
   */
  code?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 机构所属的行业
   * @format int64
   */
  industryType?: number;
  /**
   * 机构的简介
   * @minLength 0
   * @maxLength 65
   */
  introduction?: string;
  /**
   * 组织机构的全称
   * @minLength 0
   * @maxLength 21
   */
  name?: string;
  /**
   * 机构的英文名称
   * @minLength 0
   * @maxLength 64
   */
  nameEn?: string;
  /** name全路径 */
  nameFullPath?: string;
  /**
   * 父机构ID
   * @format int64
   */
  parentId?: number;
  /**
   * 人数
   * @format int32
   */
  peopleNum?: number;
  /**
   * 机构的负责人
   * @minLength 0
   * @maxLength 20
   */
  personInCharge?: string;
  /**
   * 机构的电话
   * @minLength 0
   * @maxLength 20
   */
  phone?: string;
  /**
   * 机构的代码，由代码生成
   * @minLength 0
   * @maxLength 32
   */
  serialNo?: string;
  /**
   * 机构名称的简称
   * @minLength 0
   * @maxLength 16
   */
  shortName?: string;
  /**
   * 排序号
   * @format int32
   */
  sortOrder?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/**
 * Organization对象
 * 组织机构，包括根、公司、部门等等
 */
export interface Organization对象 {
  /**
   * 机构的地址
   * @minLength 0
   * @maxLength 32
   */
  address?: string;
  /**
   * 机构所从事的业务的类别
   * @format int64
   */
  businessType?: number;
  /**
   * 机构的编码，例如PSC
   * @minLength 0
   * @maxLength 8
   */
  code?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 机构所属的行业
   * @format int64
   */
  industryType?: number;
  /**
   * 机构的简介
   * @minLength 0
   * @maxLength 65
   */
  introduction?: string;
  /**
   * 组织机构的全称
   * @minLength 0
   * @maxLength 21
   */
  name?: string;
  /**
   * 机构的英文名称
   * @minLength 0
   * @maxLength 64
   */
  nameEn?: string;
  /** name全路径 */
  nameFullPath?: string;
  /**
   * 父机构ID
   * @format int64
   */
  parentId?: number;
  /**
   * 机构的负责人
   * @minLength 0
   * @maxLength 20
   */
  personInCharge?: string;
  /**
   * 机构的电话
   * @minLength 0
   * @maxLength 20
   */
  phone?: string;
  /**
   * 机构的代码，由代码生成
   * @minLength 0
   * @maxLength 32
   */
  serialNo?: string;
  /**
   * 机构名称的简称
   * @minLength 0
   * @maxLength 16
   */
  shortName?: string;
  /**
   * 排序号
   * @format int32
   */
  sortOrder?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/** PagedResultOfListOfAuthorityGroupPageDetailVO对象 */
export interface PagedResultOfListOfAuthorityGroupPageDetailVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: AuthorityGroupPageDetailVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfEmployeeVO对象 */
export interface PagedResultOfListOfEmployeeVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: EmployeeVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfJobPositionVO对象 */
export interface PagedResultOfListOfJobPositionVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: JobPositionVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfOrganizationVO对象 */
export interface PagedResultOfListOfOrganizationVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: OrganizationVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfRoleVO对象 */
export interface PagedResultOfListOfRoleVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: RoleVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfUserEmployeeVO用户详细信息VO */
export interface PagedResultOfListOfUserEmployeeVO用户详细信息VO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: UserEmployeeVO用户详细信息VO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfUserVO对象 */
export interface PagedResultOfListOfUserVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: UserVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/**
 * RoleAddDTO新增DTO对象
 * 用于授权的角色
 */
export interface RoleAddDTO新增DTO对象 {
  /**
   * 角色的描述信息
   * @minLength 0
   * @maxLength 64
   */
  description?: string;
  /** 角色分配的资源权限 */
  featureResourceId?: number[];
  /**
   * 机构的id，由代码生成
   * @format int64
   */
  groupId?: number;
  /**
   * 角色的名称，在一个部门下必须唯一
   * @minLength 0
   * @maxLength 32
   */
  name?: string;
  /**
   * 角色状态,包括1:启用、0:停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * RoleAuthorityGroupVO对象
 * 权限组下的角色对象
 */
export interface RoleAuthorityGroupVO对象 {
  /**
   * 角色id
   * @format int64
   */
  id?: number;
  /** 角色名 */
  name?: string;
}

/**
 * RoleAuthorizationCheckDTO
 * 角色授权检查的接收对象，包括角色id集合
 */
export interface RoleAuthorizationCheckDTO {
  /** 角色ID集合 */
  roleIdList: number[];
}

/**
 * RoleAuthorizeUserDTO对象
 * 用于为角色分配授权用户
 */
export interface RoleAuthorizeUserDTO对象 {
  /** 用户ID数组 */
  arrUserId: number[];
  /**
   * 角色ID
   * @format int64
   */
  roleId: number;
}

/**
 * RoleAuthorizedUserVO对象
 * 角色已分配用户对象
 */
export interface RoleAuthorizedUserVO对象 {
  /** 用户对应人员的姓名 */
  employeeName?: string;
  /**
   * 用户id
   * @format int64
   */
  id?: number;
  /** 用户名 */
  name?: string;
}

/**
 * RoleChangeStatusDTO对象
 * 用于批量修改角色状态的对象
 */
export interface RoleChangeStatusDTO对象 {
  /** 角色ID集合 */
  arrId: number[];
  /**
   * 状态值,1-启用或0-停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status: 0 | 1 | 2 | 3 | 4;
}

/**
 * RoleDetailVO对象
 * 角色详情展示数据对象
 */
export interface RoleDetailVO对象 {
  /** 角色的描述信息 */
  description?: string;
  /**
   * 所属权限组ID
   * @format int64
   */
  groupId?: number;
  /** 角色归属权限组名称 */
  groupName?: string;
  /**
   * 角色归属权限组状态【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  groupStatus?: 0 | 1 | 2 | 3 | 4;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 角色的名称，必须唯一 */
  name?: string;
  /**
   * 角色的状态，例如启用，停用等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * RoleGroupVO对象
 * 带有归属权限组信息的角色对象
 */
export interface RoleGroupVO对象 {
  /** 归属机构名称 */
  groupName?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 角色名称 */
  roleName?: string;
}

/**
 * RolePagedDTO分页对象
 * 用于授权的角色
 */
export interface RolePagedDTO分页对象 {
  /**
   * 创建时间结束(时间戳,精确到秒)
   * @format int64
   */
  createTimestampEnd?: number;
  /**
   * 创建时间开始(时间戳,精确到秒)
   * @format int64
   */
  createTimestampStart?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 角色名或角色描述，支持模糊查询 */
  name?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /**
   * 角色的归属权限组id
   * @format int64
   */
  roleGroupId?: number;
  /**
   * 角色的状态，例如1：启用，0：停用
   * @format int32
   */
  status?: 0 | 1;
}

/**
 * RoleSearchDTO对象
 * 用于批量查询角色信息的对象
 */
export interface RoleSearchDTO对象 {
  /** 角色ID集合 */
  idList: number[];
}

/**
 * RoleUpdateDTO
 * 角色更新对象
 */
export interface RoleUpdateDTO {
  /**
   * 角色的描述信息
   * @minLength 0
   * @maxLength 64
   */
  description?: string;
  /** 角色分配的资源权限 */
  featureResourceId?: number[];
  /**
   * 角色ID
   * @format int64
   */
  id?: number;
  /**
   * 角色的名称，在一个部门下必须唯一
   * @minLength 0
   * @maxLength 32
   */
  name?: string;
  /**
   * 角色状态,包括1:启用、0:停用【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * RoleUserDTO
 * 用于查询用户或机构的已/未授权角色列表的对象
 */
export interface RoleUserDTO {
  /**
   * 用户id
   * @format int64
   */
  userId?: number;
}

/**
 * RoleUserVO对象
 * 编辑用户时使用的角色列表，分为未授权角色集和已授权角色集
 */
export interface RoleUserVO对象 {
  /** 所选机构的全部角色列表 */
  allRoleList?: RoleGroupVO对象[];
  /** 已授权角色列表 */
  authorizedRoleList?: RoleGroupVO对象[];
}

/**
 * RoleVO对象
 * 角色列表展示数据对象
 */
export interface RoleVO对象 {
  /** 角色的描述信息 */
  description?: string;
  /**
   * 所属权限组ID
   * @format int64
   */
  groupId?: number;
  /** 角色归属权限组名称 */
  groupName?: string;
  /**
   * 角色归属权限组状态【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  groupStatus?: 0 | 1 | 2 | 3 | 4;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 角色的名称，必须唯一 */
  name?: string;
  /**
   * 角色的状态，例如启用，停用等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * Role对象
 * 用于授权的角色
 */
export interface Role对象 {
  /** 角色的描述信息 */
  description?: string;
  /**
   * 所属权限组ID
   * @format int64
   */
  groupId?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 角色的名称，必须唯一 */
  name?: string;
  /**
   * 角色的状态，例如启用，停用等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/**
 * UserAddDTO新增DTO对象
 * 登录并使用系统的用户
 */
export interface UserAddDTO新增DTO对象 {
  /**
   * 人员的唯一标识，没有业务含义
   * @format int64
   */
  employeeId?: number;
  /** 用户被授予的组(GA专用) */
  groups?: number[];
  /** 用户拥有的标签 */
  labels?: UserLabelJoinDTO对象[];
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
  /** 备注 */
  remark?: string;
  /** 用户被授予的资源(CA专用) */
  resources?: number[];
  /** 用户被授予的角色 */
  roles?: number[];
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /** 用户管理的机构 */
  userManageOrganization?: number[];
  /** 用户为哪些机构工作 */
  userWorkForOrganization?: number[];
}

/**
 * UserAllVO用户完整信息
 * 用户完整信息
 */
export interface UserAllVO用户完整信息 {
  /** 用户的头像图片url */
  avatar?: string;
  /** 用户对应的员工对象 */
  employee?: EmployeeVO对象;
  /**
   * 人员的唯一标识，没有业务含义
   * @format int64
   */
  employeeId?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
  /** 用户对应的员工的部门对象 */
  organization?: OrganizationVO对象;
  /**
   * 用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】
   * @format int32
   */
  passwordStatus?: 1 | 2;
  /** 备注 */
  remark?: string;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /**
   * null【1:上班, 2:值班, 3:休假】
   * @format int32
   */
  workStatus?: 1 | 2 | 3;
}

/** UserAuthorizeDTO用户授权DTO对象 */
export interface UserAuthorizeDTO用户授权DTO对象 {
  /** 用户拥有的标签 */
  labels?: UserLabelJoinDTO对象[];
  /** 角色ID */
  roleIds?: number[];
  /** 用户ID */
  userIds?: number[];
}

/** UserBelongToOrgDTO获取归属部门用户DTO */
export interface UserBelongToOrgDTO获取归属部门用户DTO {
  /**
   * 是否包含子部门
   * @example false
   */
  children?: boolean;
  /** 用户名或姓名或工号 */
  name?: string;
  /**
   * 归属机构ID
   * @format int64
   */
  organizationId?: number;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
}

/**
 * UserCenterTenantVO
 * 用户中心租户admin账号信息
 */
export interface UserCenterTenantVO {
  /** 公司名称 */
  companyName?: string;
  /** 租户联系人的姓名 */
  contactor?: string;
  /** 租户联系人的邮箱 */
  contactorEmail?: string;
  /** 租户联系人的电话 */
  contactorPhone?: string;
  /** 人员信息 */
  emp?: EmployeeVO对象;
  /**
   * 产品许可剩余天数
   * @format int64
   */
  remainingNumberOfDays?: number;
  /**
   * 租户编号
   * @format int64
   */
  tenantId?: number;
  /** 租户名称 */
  tenantName?: string;
  /**
   * 租户状态
   * @format int32
   */
  tenantStatus?: number;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /** 系统用户 */
  user?: UserDetailVO用户详细信息VO;
  /**
   * 租户租约有效期的起始日期
   * @format date
   */
  validFrom?: string;
  /**
   * 租户租约有效期的结束日期
   * @format date
   */
  validTo?: string;
}

/** UserChangePasswordDTO用户修改密码DTO对象 */
export interface UserChangePasswordDTO用户修改密码DTO对象 {
  /** 新密码 */
  newPassword?: string;
  /** 原始密码 */
  oldPassword?: string;
  /**
   * 用户ID
   * @format int64
   */
  userId?: number;
}

/** UserChangeStatusByNameDTO用户修改状态DTO对象 */
export interface UserChangeStatusByNameDTO用户修改状态DTO对象 {
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /** 用户名 */
  userName?: string;
}

/** UserChangeStatusDTO用户修改状态DTO对象 */
export interface UserChangeStatusDTO用户修改状态DTO对象 {
  /** 用户ID */
  ids?: number[];
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
}

/** UserDetailVO用户详细信息VO */
export interface UserDetailVO用户详细信息VO {
  /** 用户管理组对象集 */
  authGroups?: AuthorityGroupVO对象[];
  /** 用户的头像图片url */
  avatar?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 人员编码 */
  employeeCode?: string;
  /**
   * 人员的唯一标识，没有业务含义
   * @format int64
   */
  employeeId?: number;
  /** 人员姓名 */
  employeeName?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender?: number;
  /** 用户管理的组 */
  groups?: number[];
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /**
   * 岗位代码(多个逗号分隔)
   * @example "0001,01AB"
   */
  jobPosition?: string;
  /** 所有可用标签，用户有的用selected=true表示 */
  labels?: LabelVO对象[];
  manageOrgs?: OrganizationVO对象[];
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
  /** 归属机构全路径 */
  nameFullPath?: string;
  /**
   * 归属机构ID
   * @format int64
   */
  organizationId?: number;
  /** 归属机构 */
  organizationName?: string;
  /**
   * 归属机构ID【0:集团, 1:公司, 2:部门】
   * @format int32
   */
  organizationType?: 0 | 1 | 2;
  /**
   * 用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】
   * @format int32
   */
  passwordStatus?: 1 | 2;
  /** 从根节点到当前节点的路径(不包含当前节点)， 例如(/r-1/c-13/d-25).  由代码自动生成，不展示给用户。用于后端查询或者生成树的时候使用 */
  path?: string;
  /** 人员的电话 */
  phone?: string;
  /** 岗位集合 */
  positionList?: JobPositionVO对象[];
  /** 备注 */
  remark?: string;
  /** 用户被授予的资源(CA专用) */
  resources?: number[];
  role?: RoleVO对象[];
  /** 用户的角色 */
  roles?: RoleGroupVO对象[];
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /** 用户管理的机构 */
  userManageOrganization?: number[];
  /** 用户为哪些机构工作 */
  userWorkForOrganization?: number[];
  workOrgs?: OrganizationVO对象[];
  /**
   * null【1:上班, 2:值班, 3:休假】
   * @format int32
   */
  workStatus?: 1 | 2 | 3;
  /**
   * 工作航站(多个逗号分隔)
   * @example "CTU,PEK"
   */
  workTerminal?: string;
  /** 微信OpenId */
  wxOpenid?: string;
}

/** UserEmployeeVO用户详细信息VO */
export interface UserEmployeeVO用户详细信息VO {
  /** 创建者 */
  createdBy?: string;
  /** 钉钉账号 */
  dingtalk?: string;
  /** 人员编码 */
  employeeCode?: string;
  /** 员工的姓名 */
  employeeName?: string;
  /** @format int64 */
  id?: number;
  /** 人员工号 */
  jobNumber?: string;
  /** 姓名或登录名 */
  name?: string;
  /** 机构全路径名称 */
  nameFullPath?: string;
  /**
   * 所属机构的一级机构ID
   * @format int64
   */
  oneLevelId?: number;
  /**
   * 所属机构ID
   * @format int64
   */
  orgId?: number;
  /** 员工所属部门名称 */
  organizationName?: string;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /**
   * 工作航站(多个逗号分隔)
   * @example "CTU,PEK"
   */
  workTerminal?: string;
  /** 微信Openid */
  wxOpenid?: string;
}

/** UserEnterpriseAdminVO企业管理员VO */
export interface UserEnterpriseAdminVO企业管理员VO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
}

/**
 * UserForPositionDTO对象
 * 用岗位查询角色信息的对象
 */
export interface UserForPositionDTO对象 {
  /** 岗位代码集合 */
  jobPositionCodeList?: string[];
}

/**
 * UserLabelJoinDTO对象
 * 用户拥有的标签
 */
export interface UserLabelJoinDTO对象 {
  /**
   * 标签ID
   * @format int64
   */
  sysLabelId?: number;
  /** 标签值ID */
  valueIds?: string[];
  /** 标签值 */
  values?: string[];
}

/**
 * UserPagedCADTO分页对象
 * CA管理员用户
 */
export interface UserPagedCADTO分页对象 {
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 管理机构ID */
  manageId?: number[];
  /** 用户名或姓名 */
  name?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 机构的类型【0:集团, 1:公司, 2:部门】
   * @format int32
   */
  type?: 0 | 1 | 2;
}

/**
 * UserPagedDTO分页对象
 * 登录并使用系统的用户
 */
export interface UserPagedDTO分页对象 {
  /**
   * 创建时间结束
   * @format date-time
   */
  createTimeEnd?: string;
  /**
   * 创建时间开始
   * @format date-time
   */
  createTimeStart?: string;
  /** 人员所属机构ID */
  employeeOrganizationId?: number[];
  /**
   * 是否判断在管理范围内
   * @example false
   */
  inManage?: boolean;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 用户名、姓名、人员邮箱 */
  name?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
}

/**
 * UserUpdateDTO用户修改DTO
 * 登录并使用系统的用户
 */
export interface UserUpdateDTO用户修改DTO {
  /** 用户被授予的组(GA专用) */
  groups?: number[];
  /**
   * 用户ID
   * @format int64
   */
  id?: number;
  /** 用户拥有的标签 */
  labels?: UserLabelJoinDTO对象[];
  /** 备注 */
  remark?: string;
  /** 用户被授予的资源(CA专用) */
  resources?: number[];
  /** 用户被授予的角色 */
  roles?: number[];
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /** 用户管理的机构 */
  userManageOrganization?: number[];
  /** 用户为哪些机构工作 */
  userWorkForOrganization?: number[];
}

/**
 * UserVO对象
 * 登录并使用系统的用户
 */
export interface UserVO对象 {
  /** 用户的头像图片url */
  avatar?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 人员编码 */
  employeeCode?: string;
  /**
   * 人员的唯一标识，没有业务含义
   * @format int64
   */
  employeeId?: number;
  /** 人员姓名 */
  employeeName?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /**
   * 岗位代码(多个逗号分隔)
   * @example "0001,01AB"
   */
  jobPosition?: string;
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
  /** 归属机构全路径 */
  nameFullPath?: string;
  /**
   * 归属机构ID
   * @format int64
   */
  organizationId?: number;
  /** 归属机构 */
  organizationName?: string;
  /**
   * 归属机构ID【0:集团, 1:公司, 2:部门】
   * @format int32
   */
  organizationType?: 0 | 1 | 2;
  /**
   * 用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】
   * @format int32
   */
  passwordStatus?: 1 | 2;
  /** 从根节点到当前节点的路径(不包含当前节点)， 例如(/r-1/c-13/d-25).  由代码自动生成，不展示给用户。用于后端查询或者生成树的时候使用 */
  path?: string;
  /** 人员的电话 */
  phone?: string;
  /** 岗位集合 */
  positionList?: JobPositionVO对象[];
  /** 备注 */
  remark?: string;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /**
   * null【1:上班, 2:值班, 3:休假】
   * @format int32
   */
  workStatus?: 1 | 2 | 3;
  /**
   * 工作航站(多个逗号分隔)
   * @example "CTU,PEK"
   */
  workTerminal?: string;
  /** 微信OpenId */
  wxOpenid?: string;
}

/** UserWorkForOrgDTO获取工作部门用户DTO */
export interface UserWorkForOrgDTO获取工作部门用户DTO {
  /**
   * 是否包含子部门
   * @example false
   */
  children?: boolean;
  /** 人员的岗位代码 */
  jobPosition?: string;
  /**
   * 员工的职务
   * @format int32
   */
  jobTitle?: number;
  /** 用户名或姓名或工号 */
  name?: string;
  /**
   * 归属机构ID
   * @format int64
   */
  organizationId?: number;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
}

/**
 * 用户人员ID对象
 * 批量导入时用
 */
export interface Type用户人员ID对象 {
  /** 创建者 */
  createdBy?: string;
  /**
   * 创建时间
   * @format date-time
   */
  createdTime?: string;
  /**
   * 人员ID
   * @format int64
   */
  empId?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 更新者 */
  updatedBy?: string;
  /**
   * 更新时间
   * @format date-time
   */
  updatedTime?: string;
  /**
   * 用户ID
   * @format int64
   */
  userId?: number;
}

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from 'axios';

import { errorConfig, requestConfig, responseConfig } from '../axiosConfig';

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, 'body' | 'method' | 'query' | 'path'>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, 'data' | 'cancelToken'> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: '' });
    this.instance.interceptors.request.use(requestConfig, errorConfig);
    this.instance.interceptors.response.use(responseConfig, errorConfig);
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === 'object' && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] = property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem));
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (type === ContentType.FormData && body && body !== null && typeof body === 'object') {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== 'string') {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type && type !== ContentType.FormData ? { 'Content-Type': type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title SPMP(Swcares Project Manage Platform) Api Doc
 * @version Application Version：3.0.6-RC-250403011712
 * @baseUrl http://**************:8082
 * @contact swcares team <<EMAIL>>
 */
export class Api<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  pmt = {
    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupAdminList
     * @summary 通过ID查询权限组管理员列表，URL传参
     * @request GET:/pmt/uc/authority/group/admin
     * @secure
     */
    ucAuthorityGroupAdminList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfAuthorityGroupManagerVO对象, void>({
        path: `/pmt/uc/authority/group/admin`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupAdminDetail
     * @summary 通过ID查询权限组管理员列表
     * @request GET:/pmt/uc/authority/group/admin/{id}
     * @secure
     */
    ucAuthorityGroupAdminDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfAuthorityGroupManagerVO对象, void>({
        path: `/pmt/uc/authority/group/admin/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupCheckboxDetail
     * @summary 获取复选框类型的权限组列表
     * @request GET:/pmt/uc/authority/group/checkbox/{userId}
     * @secure
     */
    ucAuthorityGroupCheckboxDetail: (userId: number, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfAuthorityGroupCheckBoxVO对象, void>({
        path: `/pmt/uc/authority/group/checkbox/${userId}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupDeleteCreate
     * @summary 批量删除系统功能资源
     * @request POST:/pmt/uc/authority/group/delete
     * @secure
     */
    ucAuthorityGroupDeleteCreate: (data: FeatureResourceDeleteDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/authority/group/delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupGetList
     * @summary 通过ID查询权限组信息记录，URL传参
     * @request GET:/pmt/uc/authority/group/get
     * @secure
     */
    ucAuthorityGroupGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfAuthorityGroupDetailVO对象, void>({
        path: `/pmt/uc/authority/group/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupGetDetail
     * @summary 通过ID查询权限组信息记录
     * @request GET:/pmt/uc/authority/group/get/{id}
     * @secure
     */
    ucAuthorityGroupGetDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfAuthorityGroupDetailVO对象, void>({
        path: `/pmt/uc/authority/group/get/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupListCreate
     * @summary 获取权限组列表
     * @request POST:/pmt/uc/authority/group/list
     * @secure
     */
    ucAuthorityGroupListCreate: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfAuthorityGroupVO对象, void>({
        path: `/pmt/uc/authority/group/list`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupPageCreate
     * @summary 条件分页查询权限组信息记录
     * @request POST:/pmt/uc/authority/group/page
     * @secure
     */
    ucAuthorityGroupPageCreate: (
      data: AuthorityGroupPagedDTO分页对象,
      params: RequestParams = {},
    ) =>
      this.request<PagedResultOfListOfAuthorityGroupPageDetailVO对象, void>({
        path: `/pmt/uc/authority/group/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupSaveCreate
     * @summary 新建权限组信息记录
     * @request POST:/pmt/uc/authority/group/save
     * @secure
     */
    ucAuthorityGroupSaveCreate: (data: AuthorityGroupAddDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/authority/group/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupStatusCreate
     * @summary 批量修改权限组状态
     * @request POST:/pmt/uc/authority/group/status
     * @secure
     */
    ucAuthorityGroupStatusCreate: (
      data: AuthorityGroupChgStatusDTO对象,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/authority/group/status`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupUpdateCreate
     * @summary 修改权限组信息记录
     * @request POST:/pmt/uc/authority/group/update
     * @secure
     */
    ucAuthorityGroupUpdateCreate: (data: AuthorityGroupUpdateDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/authority/group/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 权限组信息接口
     * @name UcAuthorityGroupUpdateManagerCreate
     * @summary 修改权限组关联的管理员列表
     * @request POST:/pmt/uc/authority/group/update_manager
     * @secure
     */
    ucAuthorityGroupUpdateManagerCreate: (
      data: AuthorityGroupUpdateManagerDTO对象,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/authority/group/update_manager`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 系统用户、租户用户登录登出接口
     * @name UcAuthLogoutList
     * @summary 退出登录接口
     * @request GET:/pmt/uc/auth/logout
     * @secure
     */
    ucAuthLogoutList: (params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/auth/logout`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 系统用户、租户用户登录登出接口
     * @name UcAuthMenuList
     * @summary 获取当前用户菜单,subSystemCode为子系统编号，多个用英文逗号分隔
     * @request GET:/pmt/uc/auth/menu
     * @secure
     */
    ucAuthMenuList: (
      query?: {
        /** subSystemCode */
        subSystemCode?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfMapOfstringAndobject, void>({
        path: `/pmt/uc/auth/menu`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 系统用户、租户用户登录登出接口
     * @name UcAuthPrincipalList
     * @summary user
     * @request GET:/pmt/uc/auth/principal
     * @secure
     */
    ucAuthPrincipalList: (
      query?: {
        name?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<EmployeePagedDTO分页对象, void>({
        path: `/pmt/uc/auth/principal`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 系统用户、租户用户登录登出接口
     * @name UcAuthSubSystemList
     * @summary 获取当前用户所有子系统
     * @request GET:/pmt/uc/auth/sub_system
     * @secure
     */
    ucAuthSubSystemList: (
      query?: {
        /** subSystemCode */
        subSystemCode?: string;
        /**
         * terminal
         * @format int32
         */
        terminal?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/auth/sub_system`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 系统用户、租户用户登录登出接口
     * @name UcAuthUserInfoList
     * @summary userInfo
     * @request GET:/pmt/uc/auth/user_info
     * @secure
     */
    ucAuthUserInfoList: (params: RequestParams = {}) =>
      this.request<BaseResultOfLoginUserDetails, void>({
        path: `/pmt/uc/auth/user_info`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeBatchChgOrgCreate
     * @summary 批量修改人员归属机构
     * @request POST:/pmt/uc/employee/batch_chg_org
     * @secure
     */
    ucEmployeeBatchChgOrgCreate: (data: EmployeeOrgDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/batch_chg_org`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeDeleteCreate
     * @summary 通过ID删除系统人员记录，URL传参
     * @request POST:/pmt/uc/employee/delete
     * @secure
     */
    ucEmployeeDeleteCreate: (data: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeDeleteCreate2
     * @summary 通过ID删除系统人员记录
     * @request POST:/pmt/uc/employee/delete/{id}
     * @originalName ucEmployeeDeleteCreate
     * @duplicate
     * @secure
     */
    ucEmployeeDeleteCreate2: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeEmpsModifyOrgCreate
     * @summary 人员组织机构变更迁移，批量
     * @request POST:/pmt/uc/employee/emps_modify_org
     * @secure
     */
    ucEmployeeEmpsModifyOrgCreate: (data: EmployeeOrgDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/emps_modify_org`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeGetList
     * @summary 通过ID查询系统人员记录，URL传参
     * @request GET:/pmt/uc/employee/get
     * @secure
     */
    ucEmployeeGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfEmployeeVO对象, void>({
        path: `/pmt/uc/employee/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeGetDetail
     * @summary 通过ID查询系统人员记录
     * @request GET:/pmt/uc/employee/get/{id}
     * @secure
     */
    ucEmployeeGetDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfEmployeeVO对象, void>({
        path: `/pmt/uc/employee/get/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeGetByIdsCreate
     * @summary 根据人员Id集，获取指定人员列表
     * @request POST:/pmt/uc/employee/get_by_ids
     * @secure
     */
    ucEmployeeGetByIdsCreate: (data: number[], params: RequestParams = {}) =>
      this.request<BaseResultOfListOfUserEmployeeVO用户详细信息VO, void>({
        path: `/pmt/uc/employee/get_by_ids`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeGetByMgtorgCreate
     * @summary 加载管理员管理范围的人员，返回UserEmployeeVO结构的数据集
     * @request POST:/pmt/uc/employee/get_by_mgtorg
     * @secure
     */
    ucEmployeeGetByMgtorgCreate: (data: EmployeePagedDTO分页对象, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfUserEmployeeVO用户详细信息VO, void>({
        path: `/pmt/uc/employee/get_by_mgtorg`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeGetByOrgList
     * @summary 根据指定条件查询人员信息，返回EmployeeDetailVO结构的数据集
     * @request GET:/pmt/uc/employee/get_by_org
     * @secure
     */
    ucEmployeeGetByOrgList: (
      query?: {
        /** 模糊搜索条件 */
        condition?: string;
        /** 人员ID集 */
        empIds?: number[];
        /** 通过机构ID进行查询时，是否包含子机构下人员 */
        isContainChildren?: boolean;
        /** 岗位代码 */
        jobPosition?: string;
        /** 机构ID集 */
        orgIds?: number[];
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfEmployeeVO对象, void>({
        path: `/pmt/uc/employee/get_by_org`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeGetDetailList
     * @summary 通过ID获取指定人员详细信息，包括用户账号（不包含密码）
     * @request GET:/pmt/uc/employee/get_detail
     * @secure
     */
    ucEmployeeGetDetailList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfEmployeeVO对象, void>({
        path: `/pmt/uc/employee/get_detail`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeePageCreate
     * @summary 条件分页查询系统人员记录列表
     * @request POST:/pmt/uc/employee/page
     * @secure
     */
    ucEmployeePageCreate: (data: EmployeePagedDTO分页对象, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfEmployeeVO对象, void>({
        path: `/pmt/uc/employee/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeSaveCreate
     * @summary 新增人员信息保存
     * @request POST:/pmt/uc/employee/save
     * @secure
     */
    ucEmployeeSaveCreate: (
      data: {
        /**
         * 所属机构的id
         * @format int64
         */
        belongToOrg: number;
        /**
         * 人员的出生日期
         * @format date
         */
        birthday?: string;
        /** 钉钉账号 */
        dingtalk?: string;
        /** 人员的电子邮件地址 */
        emailAddress?: string;
        /** 员工的编号 */
        employeeCode?: string;
        /**
         * file
         * @format binary
         */
        file?: File;
        /**
         * 人员的性别，1表示男性，2表示女性
         * @format int32
         */
        gender: number;
        /** 人员的毕业院校 */
        graduateFrom?: string;
        /**
         * id
         * @format int64
         */
        id?: number;
        /** 身份证号 */
        idCard?: string;
        /** 人员工号 */
        jobNumber?: string;
        /** 人员的岗位 */
        jobPosition?: string;
        /** 员工的职务 */
        jobTitle?: string;
        /** 人员的姓名 */
        name: string;
        /** 人员的电话 */
        phone: string;
        /** 人员头像的地址 */
        photoRul?: string;
        /** 人员的政治面貌 */
        politicalStatus?: string;
        /**
         * 人员当前的状态，例如在职、离职、停职等
         * @format int32
         */
        status: number;
        /**
         * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
         * @format int32
         */
        syncStatus?: number;
        /** 工作航站(多个逗号分隔) */
        workTerminal?: string;
        /** 微信OpenId */
        wxOpenid?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        format: 'document',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeSaveCreateUserCreate
     * @summary 人员新增同时创建用户
     * @request POST:/pmt/uc/employee/save_create_user
     * @secure
     */
    ucEmployeeSaveCreateUserCreate: (
      data: {
        /** 系统生成用户名 */
        autoUserName?: boolean;
        /**
         * 所属机构的id
         * @format int64
         */
        'employee.belongToOrg': number;
        /**
         * 人员的出生日期
         * @format date
         */
        'employee.birthday'?: string;
        /** 钉钉账号 */
        'employee.dingtalk'?: string;
        /** 人员的电子邮件地址 */
        'employee.emailAddress'?: string;
        /** 员工的编号 */
        'employee.employeeCode'?: string;
        /**
         * 人员的性别，1表示男性，2表示女性
         * @format int32
         */
        'employee.gender': number;
        /** 人员的毕业院校 */
        'employee.graduateFrom'?: string;
        /**
         * id
         * @format int64
         */
        'employee.id'?: number;
        /** 身份证号 */
        'employee.idCard'?: string;
        /** 人员工号 */
        'employee.jobNumber'?: string;
        /** 人员的岗位 */
        'employee.jobPosition'?: string;
        /** 员工的职务 */
        'employee.jobTitle'?: string;
        /** 人员的姓名 */
        'employee.name': string;
        /** 人员的电话 */
        'employee.phone': string;
        /** 人员头像的地址 */
        'employee.photoRul'?: string;
        /** 人员的政治面貌 */
        'employee.politicalStatus'?: string;
        /**
         * 人员当前的状态，例如在职、离职、停职等
         * @format int32
         */
        'employee.status': number;
        /**
         * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
         * @format int32
         */
        'employee.syncStatus'?: number;
        /** 工作航站(多个逗号分隔) */
        'employee.workTerminal'?: string;
        /** 微信OpenId */
        'employee.wxOpenid'?: string;
        /**
         * file
         * @format binary
         */
        file?: File;
        /** 是否创建用户 */
        isCreateUser?: boolean;
        /**
         * 人员的唯一标识，没有业务含义
         * @format int64
         */
        'user.employeeId'?: number;
        /** 用户被授予的组(GA专用) */
        'user.groups'?: number[];
        /**
         * 标签ID
         * @format int64
         */
        'user.labels[0].sysLabelId': number;
        /** 标签值ID */
        'user.labels[0].valueIds'?: string[];
        /** 标签值 */
        'user.labels[0].values'?: string[];
        /** 用户名，可用于登录系统，必须唯一。 */
        'user.name': string;
        /** 备注 */
        'user.remark'?: string;
        /** 用户被授予的资源(CA专用) */
        'user.resources'?: number[];
        /** 用户被授予的角色 */
        'user.roles'?: number[];
        /** 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等 */
        'user.status': 'DISABLED' | 'ENABLE' | 'EXPIRE' | 'LOCK' | 'NOT_ACTIVATED';
        /** 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员 */
        'user.type': 'ADMIN' | 'COM_ADMIN' | 'CONSUMER' | 'ENT_ADMIN' | 'SYSTEM';
        /** 用户管理的机构 */
        'user.userManageOrganization'?: number[];
        /** 用户为哪些机构工作 */
        'user.userWorkForOrganization'?: number[];
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/save_create_user`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        format: 'document',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeSaveEmpUserCreate
     * @summary 人员新增同时创建用户，批量
     * @request POST:/pmt/uc/employee/save_emp_user
     * @secure
     */
    ucEmployeeSaveEmpUserCreate: (data: EmployeeUserDTO[], params: RequestParams = {}) =>
      this.request<BaseResultOfMapOfstringAnd用户人员ID对象, void>({
        path: `/pmt/uc/employee/save_emp_user`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeSaveHeCreate
     * @summary 人员新增同时创建用户--不验证用户权限
     * @request POST:/pmt/uc/employee/save_he
     * @secure
     */
    ucEmployeeSaveHeCreate: (
      data: {
        /** 系统生成用户名 */
        autoUserName?: boolean;
        /**
         * 所属机构的id
         * @format int64
         */
        'employee.belongToOrg': number;
        /**
         * 人员的出生日期
         * @format date
         */
        'employee.birthday'?: string;
        /** 钉钉账号 */
        'employee.dingtalk'?: string;
        /** 人员的电子邮件地址 */
        'employee.emailAddress'?: string;
        /** 员工的编号 */
        'employee.employeeCode'?: string;
        /**
         * 人员的性别，1表示男性，2表示女性
         * @format int32
         */
        'employee.gender': number;
        /** 人员的毕业院校 */
        'employee.graduateFrom'?: string;
        /**
         * id
         * @format int64
         */
        'employee.id'?: number;
        /** 身份证号 */
        'employee.idCard'?: string;
        /** 人员工号 */
        'employee.jobNumber'?: string;
        /** 人员的岗位 */
        'employee.jobPosition'?: string;
        /** 员工的职务 */
        'employee.jobTitle'?: string;
        /** 人员的姓名 */
        'employee.name': string;
        /** 人员的电话 */
        'employee.phone': string;
        /** 人员头像的地址 */
        'employee.photoRul'?: string;
        /** 人员的政治面貌 */
        'employee.politicalStatus'?: string;
        /**
         * 人员当前的状态，例如在职、离职、停职等
         * @format int32
         */
        'employee.status': number;
        /**
         * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
         * @format int32
         */
        'employee.syncStatus'?: number;
        /** 工作航站(多个逗号分隔) */
        'employee.workTerminal'?: string;
        /** 微信OpenId */
        'employee.wxOpenid'?: string;
        /**
         * file
         * @format binary
         */
        file?: File;
        /** 是否创建用户 */
        isCreateUser?: boolean;
        /**
         * 人员的唯一标识，没有业务含义
         * @format int64
         */
        'user.employeeId'?: number;
        /** 用户被授予的组(GA专用) */
        'user.groups'?: number[];
        /**
         * 标签ID
         * @format int64
         */
        'user.labels[0].sysLabelId': number;
        /** 标签值ID */
        'user.labels[0].valueIds'?: string[];
        /** 标签值 */
        'user.labels[0].values'?: string[];
        /** 用户名，可用于登录系统，必须唯一。 */
        'user.name': string;
        /** 备注 */
        'user.remark'?: string;
        /** 用户被授予的资源(CA专用) */
        'user.resources'?: number[];
        /** 用户被授予的角色 */
        'user.roles'?: number[];
        /** 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等 */
        'user.status': 'DISABLED' | 'ENABLE' | 'EXPIRE' | 'LOCK' | 'NOT_ACTIVATED';
        /** 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员 */
        'user.type': 'ADMIN' | 'COM_ADMIN' | 'CONSUMER' | 'ENT_ADMIN' | 'SYSTEM';
        /** 用户管理的机构 */
        'user.userManageOrganization'?: number[];
        /** 用户为哪些机构工作 */
        'user.userWorkForOrganization'?: number[];
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/save_he`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        format: 'document',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeSyncCreate
     * @summary 同步人员信息到钉钉，URL传参
     * @request POST:/pmt/uc/employee/sync
     * @secure
     */
    ucEmployeeSyncCreate: (data: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/sync`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeSyncCreate2
     * @summary 同步人员信息到钉钉
     * @request POST:/pmt/uc/employee/sync/{id}
     * @originalName ucEmployeeSyncCreate
     * @duplicate
     * @secure
     */
    ucEmployeeSyncCreate2: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/sync/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人员管理接口
     * @name UcEmployeeUpdateCreate
     * @summary 修改系统人员信息保存
     * @request POST:/pmt/uc/employee/update
     * @secure
     */
    ucEmployeeUpdateCreate: (
      query: {
        /**
         * 所属机构的id
         * @format int64
         */
        belongToOrg: number;
        /**
         * 人员的出生日期
         * @format date
         */
        birthday?: string;
        /** 钉钉账号 */
        dingtalk?: string;
        /** 人员的电子邮件地址 */
        emailAddress?: string;
        /** 员工的编号 */
        employeeCode?: string;
        /**
         * 人员的性别，1表示男性，2表示女性
         * @format int32
         */
        gender: number;
        /** 人员的毕业院校 */
        graduateFrom?: string;
        /**
         * id
         * @format int64
         */
        id?: number;
        /** 身份证号 */
        idCard?: string;
        /** 人员工号 */
        jobNumber?: string;
        /** 人员的岗位 */
        jobPosition?: string;
        /** 员工的职务 */
        jobTitle?: string;
        /** 人员的姓名 */
        name: string;
        /** 人员的电话 */
        phone: string;
        /** 人员头像的地址 */
        photoRul?: string;
        /** 人员的政治面貌 */
        politicalStatus?: string;
        /**
         * 人员当前的状态，例如在职、离职、停职等
         * @format int32
         */
        status: number;
        /**
         * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
         * @format int32
         */
        syncStatus?: number;
        /** 工作航站(多个逗号分隔) */
        workTerminal?: string;
        /** 微信OpenId */
        wxOpenid?: string;
      },
      data: {
        /**
         * file
         * @format binary
         */
        file?: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/employee/update`,
        method: 'POST',
        query: query,
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 附件操作
     * @name UcFatDeleteList
     * @summary 删除文件，URL传参
     * @request GET:/pmt/uc/fat/delete
     * @secure
     */
    ucFatDeleteList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/fat/delete`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 附件操作
     * @name UcFatDeleteDetail
     * @summary 删除文件
     * @request GET:/pmt/uc/fat/delete/{id}
     * @secure
     */
    ucFatDeleteDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/fat/delete/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 附件操作
     * @name UcFatDownloadList
     * @summary 下载文件
     * @request GET:/pmt/uc/fat/download
     * @secure
     */
    ucFatDownloadList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/fat/download`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * @description 参数：file:上传文件,ducket_name:文件目录名,user_name:用户名
     *
     * @tags 附件操作
     * @name UcFatUploadCreate
     * @summary 上传文件
     * @request POST:/pmt/uc/fat/upload
     * @secure
     */
    ucFatUploadCreate: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfFileAttachment对象, void>({
        path: `/pmt/uc/fat/upload`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionDeleteCreate
     * @summary 通过ID删除岗位信息表记录
     * @request POST:/pmt/uc/job/position/delete
     * @secure
     */
    ucJobPositionDeleteCreate: (data: JobPositionDeleteDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/job/position/delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionGetList
     * @summary 通过ID查询岗位信息表记录
     * @request GET:/pmt/uc/job/position/get
     * @secure
     */
    ucJobPositionGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfJobPositionVO对象, void>({
        path: `/pmt/uc/job/position/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionGetByUserList
     * @summary 通过用户ID查询其所有岗位信息
     * @request GET:/pmt/uc/job/position/get_by_user
     * @secure
     */
    ucJobPositionGetByUserList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfJobPositionVO对象, void>({
        path: `/pmt/uc/job/position/get_by_user`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionGetComboList
     * @summary 获取岗位列表复选框，按类型分组
     * @request GET:/pmt/uc/job/position/get_combo
     * @secure
     */
    ucJobPositionGetComboList: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfJobPositionComboVO对象, void>({
        path: `/pmt/uc/job/position/get_combo`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionListCreate
     * @summary 条件分页查询岗位信息表记录
     * @request POST:/pmt/uc/job/position/list
     * @secure
     */
    ucJobPositionListCreate: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfJobPositionVO对象, void>({
        path: `/pmt/uc/job/position/list`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionPageCreate
     * @summary 条件分页查询岗位信息表记录
     * @request POST:/pmt/uc/job/position/page
     * @secure
     */
    ucJobPositionPageCreate: (data: JobPositionPagedDTO分页对象, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfJobPositionVO对象, void>({
        path: `/pmt/uc/job/position/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionSaveCreate
     * @summary 新建岗位信息表记录
     * @request POST:/pmt/uc/job/position/save
     * @secure
     */
    ucJobPositionSaveCreate: (data: JobPositionDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfJobPosition对象, void>({
        path: `/pmt/uc/job/position/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 岗位信息表接口
     * @name UcJobPositionUpdateCreate
     * @summary 修改岗位信息表记录
     * @request POST:/pmt/uc/job/position/update
     * @secure
     */
    ucJobPositionUpdateCreate: (data: JobPositionDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfJobPosition对象, void>({
        path: `/pmt/uc/job/position/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户登录资源访问接口
     * @name LoginAmendPwdList
     * @summary 找回密码--第三步修改密码
     * @request GET:/pmt/login/amend_pwd
     * @secure
     */
    loginAmendPwdList: (
      query: {
        /** 账号：手机、邮箱 */
        account: string;
        /** 验证码 */
        code: string;
        /** 新密码 */
        password: string;
        /**
         * 密码找回类型,1手机，2邮件
         * @format int32
         */
        type?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/login/amend_pwd`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户登录资源访问接口
     * @name LoginCheckCodeList
     * @summary 找回密码--第二步验证
     * @request GET:/pmt/login/check_code
     * @secure
     */
    loginCheckCodeList: (
      query: {
        /** account */
        account: string;
        /**
         * type
         * @format int32
         */
        type: number;
        /** code */
        code: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/login/check_code`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户登录资源访问接口
     * @name LoginLoginCaptchaList
     * @summary 登录发送验证码
     * @request GET:/pmt/login/login_captcha
     * @secure
     */
    loginLoginCaptchaList: (
      query: {
        /** account */
        account: string;
        /**
         * type
         * @format int32
         */
        type: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/login/login_captcha`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户登录资源访问接口
     * @name LoginNameExistList
     * @summary 检查用户名是否重复:用户名、工号、邮箱、手机号
     * @request GET:/pmt/login/name_exist
     * @secure
     */
    loginNameExistList: (
      query: {
        /** userName */
        userName: string;
        /**
         * employeeId
         * @format int64
         */
        employeeId?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/login/name_exist`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户登录资源访问接口
     * @name LoginPwdStrategyList
     * @summary 获取密码策略
     * @request GET:/pmt/login/pwd_strategy
     * @secure
     */
    loginPwdStrategyList: (params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/login/pwd_strategy`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户登录资源访问接口
     * @name LoginSendCaptchaList
     * @summary 密码找回发送验证码
     * @request GET:/pmt/login/send_captcha
     * @secure
     */
    loginSendCaptchaList: (
      query: {
        /** account */
        account: string;
        /**
         * type
         * @format int32
         */
        type: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/login/send_captcha`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationDeleteCreate
     * @summary 通过ID删除组织机构，包括根、公司、部门等等记录，URL传参
     * @request POST:/pmt/uc/organization/delete
     * @secure
     */
    ucOrganizationDeleteCreate: (data: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/organization/delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationDeleteCreate2
     * @summary 通过ID删除组织机构，包括根、公司、部门等等记录
     * @request POST:/pmt/uc/organization/delete/{id}
     * @originalName ucOrganizationDeleteCreate
     * @duplicate
     * @secure
     */
    ucOrganizationDeleteCreate2: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/organization/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationGetList
     * @summary 通过ID查询组织机构，包括根、公司、部门等等记录，URL传参
     * @request GET:/pmt/uc/organization/get
     * @secure
     */
    ucOrganizationGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfOrganizationVO对象, void>({
        path: `/pmt/uc/organization/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationGetDetail
     * @summary 通过ID查询组织机构，包括根、公司、部门等等记录
     * @request GET:/pmt/uc/organization/get/{id}
     * @secure
     */
    ucOrganizationGetDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfOrganizationVO对象, void>({
        path: `/pmt/uc/organization/get/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationGetByIdsCreate
     * @summary 根据机构ID集，获取对应的机构对象集, 一次参数最大个数为999
     * @request POST:/pmt/uc/organization/get_by_ids
     * @secure
     */
    ucOrganizationGetByIdsCreate: (data: number[], params: RequestParams = {}) =>
      this.request<BaseResultOfListOfOrganization对象, void>({
        path: `/pmt/uc/organization/get_by_ids`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationPageCreate
     * @summary 分页
     * @request POST:/pmt/uc/organization/page
     * @secure
     */
    ucOrganizationPageCreate: (data: OrganizationPagedDTO分页对象, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfOrganizationVO对象, void>({
        path: `/pmt/uc/organization/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationSaveCreate
     * @summary 新建组织机构，包括根、公司、部门等等记录
     * @request POST:/pmt/uc/organization/save
     * @secure
     */
    ucOrganizationSaveCreate: (data: OrganizationDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/organization/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构，包括根、公司、部门等等接口
     * @name UcOrganizationUpdateCreate
     * @summary 修改组织机构，包括根、公司、部门等等记录
     * @request POST:/pmt/uc/organization/update
     * @secure
     */
    ucOrganizationUpdateCreate: (data: OrganizationDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/organization/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleAuthorizationCheckCreate
     * @summary 检查列表中是否存在已授权给用户的角色，true为已存在，false为不存在，用于角色停用、删除时做校验
     * @request POST:/pmt/uc/role/authorization_check
     * @secure
     */
    ucRoleAuthorizationCheckCreate: (data: RoleAuthorizationCheckDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfboolean, void>({
        path: `/pmt/uc/role/authorization_check`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleAuthorizeCreate
     * @summary 为角色分配授权用户
     * @request POST:/pmt/uc/role/authorize
     * @secure
     */
    ucRoleAuthorizeCreate: (data: RoleAuthorizeUserDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/role/authorize`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleAuthorizedUserList
     * @summary 通过ID查询已授权用户列表，URL传参
     * @request GET:/pmt/uc/role/authorized_user
     * @secure
     */
    ucRoleAuthorizedUserList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfRoleAuthorizedUserVO对象, void>({
        path: `/pmt/uc/role/authorized_user`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleAuthorizedUserDetail
     * @summary 通过ID查询已授权用户列表
     * @request GET:/pmt/uc/role/authorized_user/{id}
     * @secure
     */
    ucRoleAuthorizedUserDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfRoleAuthorizedUserVO对象, void>({
        path: `/pmt/uc/role/authorized_user/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleDeleteCreate
     * @summary 通过ID删除角色，URL传参
     * @request POST:/pmt/uc/role/delete
     * @secure
     */
    ucRoleDeleteCreate: (data: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/role/delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleDeleteCreate2
     * @summary 通过ID删除角色
     * @request POST:/pmt/uc/role/delete/{id}
     * @originalName ucRoleDeleteCreate
     * @duplicate
     * @secure
     */
    ucRoleDeleteCreate2: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/role/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleGetList
     * @summary 通过ID查询角色详细信息，URL传参
     * @request GET:/pmt/uc/role/get
     * @secure
     */
    ucRoleGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfRoleDetailVO对象, void>({
        path: `/pmt/uc/role/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleGetDetail
     * @summary 通过ID查询角色详细信息
     * @request GET:/pmt/uc/role/get/{id}
     * @secure
     */
    ucRoleGetDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfRoleDetailVO对象, void>({
        path: `/pmt/uc/role/get/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleGetByResourceDetail
     * @summary 根据资源ID，获取拥有此资源权限的所有角色
     * @request GET:/pmt/uc/role/get_by_resource/{id}
     * @secure
     */
    ucRoleGetByResourceDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfRoleVO对象, void>({
        path: `/pmt/uc/role/get_by_resource/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleGetByUserCreate
     * @summary 当userId不为空时，返回用户的已授权、未授权角色集合；当userId为空时，返回对应orgId的所有角色集合
     * @request POST:/pmt/uc/role/get_by_user
     * @secure
     */
    ucRoleGetByUserCreate: (data: RoleUserDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfRoleUserVO对象, void>({
        path: `/pmt/uc/role/get_by_user`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleListCreate
     * @summary 通过ID集合查询角色详细信息列表
     * @request POST:/pmt/uc/role/list
     * @secure
     */
    ucRoleListCreate: (data: RoleSearchDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfRoleDetailVO对象, void>({
        path: `/pmt/uc/role/list`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRolePageCreate
     * @summary 条件分页查询角色列表
     * @request POST:/pmt/uc/role/page
     * @secure
     */
    ucRolePageCreate: (data: RolePagedDTO分页对象, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfRoleVO对象, void>({
        path: `/pmt/uc/role/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleSaveCreate
     * @summary 新建角色
     * @request POST:/pmt/uc/role/save
     * @secure
     */
    ucRoleSaveCreate: (data: RoleAddDTO新增DTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfRole对象, void>({
        path: `/pmt/uc/role/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleStatusCreate
     * @summary 批量修改角色状态
     * @request POST:/pmt/uc/role/status
     * @secure
     */
    ucRoleStatusCreate: (data: RoleChangeStatusDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/role/status`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 角色管理接口
     * @name UcRoleUpdateCreate
     * @summary 修改角色
     * @request POST:/pmt/uc/role/update
     * @secure
     */
    ucRoleUpdateCreate: (data: RoleUpdateDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/role/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户中心接口
     * @name UcUserCenterChangeAvatarCreate
     * @summary 修改用户头像
     * @request POST:/pmt/uc/user_center/change_avatar
     * @secure
     */
    ucUserCenterChangeAvatarCreate: (
      data: {
        /**
         * avatar
         * @format binary
         */
        avatar?: File;
        /**
         * id
         * @format int64
         */
        id?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfFileAttachment对象, void>({
        path: `/pmt/uc/user_center/change_avatar`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        format: 'document',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户中心接口
     * @name UcUserCenterChangeAvatarCreate2
     * @summary 修改用户头像
     * @request POST:/pmt/uc/user_center/change_avatar/{id}
     * @originalName ucUserCenterChangeAvatarCreate
     * @duplicate
     * @secure
     */
    ucUserCenterChangeAvatarCreate2: (
      id: number,
      data: {
        /**
         * avatar
         * @format binary
         */
        avatar?: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfFileAttachment对象, void>({
        path: `/pmt/uc/user_center/change_avatar/${id}`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        format: 'document',
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户中心接口
     * @name UcUserCenterGetUserInfoList
     * @summary 获取当前用户个人中心信息
     * @request GET:/pmt/uc/user_center/get_user_info
     * @secure
     */
    ucUserCenterGetUserInfoList: (params: RequestParams = {}) =>
      this.request<BaseResultOfUserCenterTenantVO, void>({
        path: `/pmt/uc/user_center/get_user_info`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserBatchAuthCreate
     * @summary 批量授权
     * @request POST:/pmt/uc/user/batch_auth
     * @secure
     */
    ucUserBatchAuthCreate: (data: UserAuthorizeDTO用户授权DTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/batch_auth`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserBatchAuthDeleteCreate
     * @summary 批量删除授权
     * @request POST:/pmt/uc/user/batch_auth_delete
     * @secure
     */
    ucUserBatchAuthDeleteCreate: (
      data: UserAuthorizeDTO用户授权DTO对象,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/batch_auth_delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserBelongToOrgCreate
     * @summary 根据部门ID，获取归属机构在这个部门下的用户id
     * @request POST:/pmt/uc/user/belong_to_org
     * @secure
     */
    ucUserBelongToOrgCreate: (
      data: UserBelongToOrgDTO获取归属部门用户DTO,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfUserEmployeeVO用户详细信息VO, void>({
        path: `/pmt/uc/user/belong_to_org`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserChangeStatusCreate
     * @summary 用户状态变更（启用/停用/解锁)
     * @request POST:/pmt/uc/user/change_status
     * @secure
     */
    ucUserChangeStatusCreate: (
      data: UserChangeStatusDTO用户修改状态DTO对象,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/change_status`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserChangeStatusByNameCreate
     * @summary 通过用户名修改用户状态（启用/停用/解锁)
     * @request POST:/pmt/uc/user/change_status_by_name
     * @secure
     */
    ucUserChangeStatusByNameCreate: (
      data: UserChangeStatusByNameDTO用户修改状态DTO对象,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/change_status_by_name`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserChoosePageCreate
     * @summary 选择用户条件分页查询
     * @request POST:/pmt/uc/user/choose_page
     * @secure
     */
    ucUserChoosePageCreate: (data: UserPagedDTO分页对象, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfUserEmployeeVO用户详细信息VO, void>({
        path: `/pmt/uc/user/choose_page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserDeleteCreate
     * @summary 通过ID删除用户记录，URL传参
     * @request POST:/pmt/uc/user/delete
     * @secure
     */
    ucUserDeleteCreate: (data: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/delete`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserDeleteCreate2
     * @summary 通过ID删除用户记录
     * @request POST:/pmt/uc/user/delete/{id}
     * @originalName ucUserDeleteCreate
     * @duplicate
     * @secure
     */
    ucUserDeleteCreate2: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * @description 在header中传租户ID：tenantId
     *
     * @tags 用户接口
     * @name UcUserEntAdminList
     * @summary 获取企业管理员
     * @request GET:/pmt/uc/user/ent_admin
     * @secure
     */
    ucUserEntAdminList: (params: RequestParams = {}) =>
      this.request<BaseResultOfUserEnterpriseAdminVO企业管理员VO, void>({
        path: `/pmt/uc/user/ent_admin`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserGetList
     * @summary 用户详情显示接口，URL传参
     * @request GET:/pmt/uc/user/get
     * @secure
     */
    ucUserGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfUserDetailVO用户详细信息VO, void>({
        path: `/pmt/uc/user/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserGetDetail
     * @summary 用户详情显示接口
     * @request GET:/pmt/uc/user/get/{id}
     * @secure
     */
    ucUserGetDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfUserDetailVO用户详细信息VO, void>({
        path: `/pmt/uc/user/get/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserGetByEmpidList
     * @summary 根据人员ID获取用户信息
     * @request GET:/pmt/uc/user/get_by_empid
     * @secure
     */
    ucUserGetByEmpidList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        empId: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfUserDetailVO用户详细信息VO, void>({
        path: `/pmt/uc/user/get_by_empid`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserGetByIdCardList
     * @summary 根据身份证查用户信息
     * @request GET:/pmt/uc/user/get_by_id_card
     * @secure
     */
    ucUserGetByIdCardList: (
      query: {
        /** idCard */
        idCard: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfUserVO对象, void>({
        path: `/pmt/uc/user/get_by_id_card`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserGetByIdsCreate
     * @summary 根据用户ID，获取用户对象，一次最多支持（999）个。如需登录即可访问，使用/common/user/get_by_ids接口。
     * @request POST:/pmt/uc/user/get_by_ids
     * @secure
     */
    ucUserGetByIdsCreate: (data: number[], params: RequestParams = {}) =>
      this.request<BaseResultOfListOfUserAllVO用户完整信息, void>({
        path: `/pmt/uc/user/get_by_ids`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserGetByPosCreate
     * @summary 根据岗位代码获取用户
     * @request POST:/pmt/uc/user/get_by_pos
     * @secure
     */
    ucUserGetByPosCreate: (data: UserForPositionDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfUserVO对象, void>({
        path: `/pmt/uc/user/get_by_pos`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserModifyPwdCreate
     * @summary 用户修改密码
     * @request POST:/pmt/uc/user/modify_pwd
     * @secure
     */
    ucUserModifyPwdCreate: (
      data: UserChangePasswordDTO用户修改密码DTO对象,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/modify_pwd`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserPageCreate
     * @summary 条件分页查询用户记录
     * @request POST:/pmt/uc/user/page
     * @secure
     */
    ucUserPageCreate: (data: UserPagedDTO分页对象, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfUserVO对象, void>({
        path: `/pmt/uc/user/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserPageCaCreate
     * @summary 条件分页查询用户记录
     * @request POST:/pmt/uc/user/page_ca
     * @secure
     */
    ucUserPageCaCreate: (data: UserPagedCADTO分页对象, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfUserVO对象, void>({
        path: `/pmt/uc/user/page_ca`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserResetPwdCreate
     * @summary 用户密码重置接口，URL传参
     * @request POST:/pmt/uc/user/reset_pwd
     * @secure
     */
    ucUserResetPwdCreate: (data: number, params: RequestParams = {}) =>
      this.request<BaseResultOfstring, void>({
        path: `/pmt/uc/user/reset_pwd`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserResetPwdCreate2
     * @summary 用户密码重置接口
     * @request POST:/pmt/uc/user/reset_pwd/{id}
     * @originalName ucUserResetPwdCreate
     * @duplicate
     * @secure
     */
    ucUserResetPwdCreate2: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfstring, void>({
        path: `/pmt/uc/user/reset_pwd/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserResetPwdByNameCreate
     * @summary 通过用户名重置用户密码接口
     * @request POST:/pmt/uc/user/reset_pwd_by_name
     * @secure
     */
    ucUserResetPwdByNameCreate: (
      query: {
        /** 用户名 */
        username: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfstring, void>({
        path: `/pmt/uc/user/reset_pwd_by_name`,
        method: 'POST',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserSaveCreate
     * @summary 新建用户记录
     * @request POST:/pmt/uc/user/save
     * @secure
     */
    ucUserSaveCreate: (data: UserAddDTO新增DTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfstring, void>({
        path: `/pmt/uc/user/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserUpdateCreate
     * @summary 修改用户记录
     * @request POST:/pmt/uc/user/update
     * @secure
     */
    ucUserUpdateCreate: (data: UserUpdateDTO用户修改DTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/uc/user/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户接口
     * @name UcUserWorkForOrgCreate
     * @summary 根据部门ID，获取工作机构在这个部门的人员
     * @request POST:/pmt/uc/user/work_for_org
     * @secure
     */
    ucUserWorkForOrgCreate: (
      data: UserWorkForOrgDTO获取工作部门用户DTO,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfUserEmployeeVO用户详细信息VO, void>({
        path: `/pmt/uc/user/work_for_org`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),
  };
}
