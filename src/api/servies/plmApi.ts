/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** BaseResultOfProjectInfoVO */
export interface BaseResultOfProjectInfoVO {
  /** @format int32 */
  code?: number;
  /** 项目信息 */
  data?: ProjectInfoVO;
  message?: string;
}

/** BaseResultOfProjectMilestoneVO */
export interface BaseResultOfProjectMilestoneVO {
  /** @format int32 */
  code?: number;
  /** 项目里程碑 */
  data?: ProjectMilestoneVO;
  message?: string;
}

/** BaseResultOfProjectMonthCostVO */
export interface BaseResultOfProjectMonthCostVO {
  /** @format int32 */
  code?: number;
  /** 项目月成本 */
  data?: ProjectMonthCostVO;
  message?: string;
}

/** BaseResultOfProjectMonthPaceVO */
export interface BaseResultOfProjectMonthPaceVO {
  /** @format int32 */
  code?: number;
  /** 项目月进度 */
  data?: ProjectMonthPaceVO;
  message?: string;
}

/** BaseResultOfProjectMonthPlanVO */
export interface BaseResultOfProjectMonthPlanVO {
  /** @format int32 */
  code?: number;
  /** 项目月计划 */
  data?: ProjectMonthPlanVO;
  message?: string;
}

/** BaseResultOfProjectRiskTrackVO */
export interface BaseResultOfProjectRiskTrackVO {
  /** @format int32 */
  code?: number;
  /** 项目风险VO */
  data?: ProjectRiskTrackVO;
  message?: string;
}

/** BaseResultOfProjectWeekPlanVO */
export interface BaseResultOfProjectWeekPlanVO {
  /** @format int32 */
  code?: number;
  /** 项目周计划VO */
  data?: ProjectWeekPlanVO;
  message?: string;
}

/** BaseResultOfProjectWeeklyVO */
export interface BaseResultOfProjectWeeklyVO {
  /** @format int32 */
  code?: number;
  /** 项目周报VO */
  data?: ProjectWeeklyVO;
  message?: string;
}

/** BaseResultOfProjectYearStatVO */
export interface BaseResultOfProjectYearStatVO {
  /** @format int32 */
  code?: number;
  /** 项目年统计VO */
  data?: ProjectYearStatVO;
  message?: string;
}

/** BaseResultOfobject */
export interface BaseResultOfobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** DateHandler */
export interface DateHandler {
  dateType?: 'DAY' | 'MONTH' | 'OTHER' | 'SEASON' | 'THIRTY' | 'WEEK' | 'YEAR';
  end?: SearchDate;
  hisEnd?: SearchDate;
  hisStart?: SearchDate;
  hisType?: 'qoq' | 'yoy';
  seedDate?: string;
  start?: SearchDate;
}

/** LoginUser */
export interface LoginUser {
  /** @format int64 */
  employeeId?: number;
  /** @format int32 */
  gender?: number;
  /** @format int64 */
  id?: number;
  jobNumber?: string;
  manageOrganizations?: number[];
  /** @format int64 */
  organizationId?: number;
  roleIds?: number[];
  scope?: 'PM' | 'PMO' | 'TEAM' | 'UNIT';
  /** @format int64 */
  tenantId?: number;
  /** @format int32 */
  userType?: number;
  username?: string;
}

/** OrderItem */
export interface OrderItem {
  asc?: boolean;
  column?: string;
}

/** PagedResultOfListOfProjectInfoVO */
export interface PagedResultOfListOfProjectInfoVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectInfoVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectMilestoneVO */
export interface PagedResultOfListOfProjectMilestoneVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectMilestoneVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectMonthCostVO */
export interface PagedResultOfListOfProjectMonthCostVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectMonthCostVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectMonthPaceVO */
export interface PagedResultOfListOfProjectMonthPaceVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectMonthPaceVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectMonthPlanVO */
export interface PagedResultOfListOfProjectMonthPlanVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectMonthPlanVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectRiskTrackVO */
export interface PagedResultOfListOfProjectRiskTrackVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectRiskTrackVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectWeekPlanVO */
export interface PagedResultOfListOfProjectWeekPlanVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectWeekPlanVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectWeeklyVO */
export interface PagedResultOfListOfProjectWeeklyVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectWeeklyVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfProjectYearStatVO */
export interface PagedResultOfListOfProjectYearStatVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: ProjectYearStatVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/**
 * ProjectInfoDTO
 * 项目信息
 */
export interface ProjectInfoDTO {
  /**
   * 内部立项时间
   * @format date
   */
  approvalDate?: string;
  /**
   * 预算内项目(0-否,1-是)
   * @format int32
   */
  budgetProject?: number;
  /** 客户单位 */
  customerName?: string;
  /**
   * 所属交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 项目经理工号 */
  jobNumber?: string;
  /**
   * 所属经营单元
   * @format int64
   */
  managementUnit?: number;
  /**
   * 计划结项时间
   * @format date
   */
  planClosingDate?: string;
  /**
   * 计划验收时间
   * @format date
   */
  planEndDate?: string;
  /**
   * 计划启动时间
   * @format date
   */
  planStartDate?: string;
  /**
   * 税前收入
   * @format bigdecimal
   */
  pretaxIncome?: number;
  /**
   * 优先级
   * @format int32
   */
  priorityLevel?: number;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编号 */
  projectCode?: string;
  /** 项目经理 */
  projectManager?: string;
  /** 项目名称 */
  projectName?: string;
  /**
   * 项目性质
   * @format int32
   */
  projectNature?: number;
  /** 项目编码 */
  projectSn?: string;
  /**
   * 项目阶段
   * @format int32
   */
  projectStage?: number;
  /**
   * 项目状态
   * @format int32
   */
  projectState?: number;
  /**
   * 税后收入
   * @format bigdecimal
   */
  revenue?: number;
}

/**
 * ProjectInfoPagedDTO
 * 项目信息PagedDTO
 */
export interface ProjectInfoPagedDTO {
  /**
   * 预算内项目(0-否,1-是)
   * @format int32
   */
  budgetProject?: number;
  /**
   * 成本风险
   * @format int32
   */
  costRisk?: number;
  /** 客户名称 */
  customerName?: string;
  /**
   * 所属交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 进度风险
   * @format int32
   */
  paceRisk?: number;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /**
   * 优先级
   * @format int32
   */
  priorityLevel?: number;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目经理 */
  projectManager?: string;
  /** 项目名称 */
  projectName?: string;
  /**
   * 项目性质
   * @format int32
   */
  projectNature?: number;
  /** 项目编号 */
  projectSn?: string;
  /**
   * 项目阶段
   * @format int32
   */
  projectStage?: number;
  /**
   * 项目状态
   * @format int32
   */
  projectState?: number;
  /** 项目年 */
  projectYear?: string;
  /** 统计截止日期 */
  statDate?: string;
}

/**
 * ProjectInfoVO
 * 项目信息
 */
export interface ProjectInfoVO {
  /**
   * 立项时间
   * @format date
   */
  approvalDate?: string;
  /**
   * 预算内项目(0-否,1-是)
   * @format int32
   */
  budgetProject?: number;
  /** 客户名称 */
  customerName?: string;
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /**
   * 计划结项时间
   * @format date
   */
  planClosingDate?: string;
  /**
   * 计划总成本
   * @format bigdecimal
   */
  planCostTotal?: number;
  /**
   * 计划验收时间
   * @format date
   */
  planEndDate?: string;
  /**
   * 计划启动时间
   * @format date
   */
  planStartDate?: string;
  /**
   * 税前收入
   * @format bigdecimal
   */
  pretaxIncome?: number;
  /**
   * 优先级
   * @format int32
   */
  priorityLevel?: number;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编号 */
  projectCode?: string;
  /** 项目经理 */
  projectManager?: string;
  /** 项目名称 */
  projectName?: string;
  /**
   * 项目性质
   * @format int32
   */
  projectNature?: number;
  /** 项目编码 */
  projectSn?: string;
  /**
   * 项目阶段
   * @format int32
   */
  projectStage?: number;
  /**
   * 项目状态
   * @format int32
   */
  projectState?: number;
  /** 备注 */
  remark?: string;
  /**
   * 税后收入
   * @format bigdecimal
   */
  revenue?: number;
  /**
   * 年度计划工量
   * @format bigdecimal
   */
  yearPlanCost?: number;
}

/**
 * ProjectMilestoneDTO
 * 项目里程碑DTO
 */
export interface ProjectMilestoneDTO {
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 项目经理工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 里程碑名称 */
  milestoneName?: string;
  /**
   * 计划工量（人天）
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /**
   * 项目年
   * @format int32
   */
  projectYear?: number;
  /** 备注 */
  remark?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /**
   * 工作日（天）
   * @format int32
   */
  workDay?: number;
}

/**
 * ProjectMilestonePagedDTO
 * 项目里程碑
 */
export interface ProjectMilestonePagedDTO {
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 里程碑名称 */
  milestoneName?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /**
   * 计划工量（人天）
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /**
   * 项目年
   * @format int32
   */
  projectYear?: number;
  /** 备注 */
  remark?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /**
   * 工作日（天）
   * @format int32
   */
  workDay?: number;
}

/**
 * ProjectMilestoneVO
 * 项目里程碑
 */
export interface ProjectMilestoneVO {
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 项目经理工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 里程碑名称 */
  milestoneName?: string;
  /**
   * 计划工量（人天）
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /**
   * 项目年
   * @format int32
   */
  projectYear?: number;
  /** 备注 */
  remark?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /**
   * 工作日（天）
   * @format int32
   */
  workDay?: number;
}

/**
 * ProjectMonthCostDTO
 * 项目月成本
 */
export interface ProjectMonthCostDTO {
  /**
   * 成本偏差率
   * @format bigdecimal
   */
  costOffset?: number;
  /**
   * 成本风险
   * @format int32
   */
  costRisk?: number;
  /**
   * 编码使用成本
   * @format bigdecimal
   */
  finishCodingWorkCost?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /**
   * 当月编码成本
   * @format bigdecimal
   */
  monthCodingWorkCost?: number;
  /**
   * 计划编码工量
   * @format bigdecimal
   */
  planCodingWorkLoad?: number;
  /**
   * 计划当月编码产出
   * @format bigdecimal
   */
  planMonthCodingProduce?: number;
  /**
   * 计划人工工量
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /** 项目年 */
  projectYear?: string;
  /**
   * 人工补充预警
   * @format int32
   */
  staffEarlyWarning?: number;
  /**
   * 统计日期
   * @format date
   */
  statDate?: string;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * ProjectMonthCostPagedDTO
 * 项目月成本PagedDTO
 */
export interface ProjectMonthCostPagedDTO {
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /** 项目年 */
  projectYear?: string;
  /**
   * 统计日期
   * @format date
   */
  statDate?: string;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * ProjectMonthCostVO
 * 项目月成本
 */
export interface ProjectMonthCostVO {
  /**
   * 成本偏差率
   * @format bigdecimal
   */
  costOffset?: number;
  /**
   * 成本风险
   * @format int32
   */
  costRisk?: number;
  /**
   * 编码使用成本
   * @format bigdecimal
   */
  finishCodingWorkCost?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /**
   * 当月编码成本
   * @format bigdecimal
   */
  monthCodingWorkCost?: number;
  /**
   * 计划编码工量
   * @format bigdecimal
   */
  planCodingWorkLoad?: number;
  /**
   * 计划当月编码产出
   * @format bigdecimal
   */
  planMonthCodingProduce?: number;
  /**
   * 计划人工工量
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /** 项目年 */
  projectYear?: string;
  /**
   * 人工补充预警
   * @format int32
   */
  staffEarlyWarning?: number;
  /**
   * 统计日期
   * @format date
   */
  statDate?: string;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * ProjectMonthPacePagedDTO
 * 项目月进度PagedDTO
 */
export interface ProjectMonthPacePagedDTO {
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 项目经理工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /**
   * 项目年
   * @format int32
   */
  projectYear?: number;
  /**
   * 统计日期
   * @format date
   */
  statDate?: string;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * ProjectMonthPaceVO
 * 项目月进度
 */
export interface ProjectMonthPaceVO {
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 项目经理工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /**
   * 里程碑滞后数量
   * @format int32
   */
  milestoneOverdue?: number;
  /**
   * 进度偏差
   * @format bigdecimal
   */
  paceOffset?: number;
  /**
   * 进度风险
   * @format int32
   */
  paceRisk?: number;
  /**
   * 当月累计PV(人天)
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /**
   * 项目年
   * @format int32
   */
  projectYear?: number;
  /**
   * 人工补充预警
   * @format int32
   */
  staffEarlyWarning?: number;
  /**
   * 统计日期
   * @format date
   */
  statDate?: string;
  /** 统计月份 */
  statMonth?: string;
  /**
   * 当月累计EV(人天)
   * @format bigdecimal
   */
  workLoadTotal?: number;
}

/**
 * ProjectMonthPlanPagedDTO
 * 项目月计划PagedDTO
 */
export interface ProjectMonthPlanPagedDTO {
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 项目经理工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 计划月份 */
  planMonth?: string;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /** 项目年 */
  projectYear?: string;
  /**
   * 统计日期
   * @format date
   */
  statDate?: string;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * ProjectMonthPlanVO
 * 项目月计划
 */
export interface ProjectMonthPlanVO {
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 项目经理工号 */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 计划编号 */
  planCode?: string;
  /** 计划月份 */
  planMonth?: string;
  /**
   * 当月PV(人天)
   * @format bigdecimal
   */
  planMonthWorkLoad?: number;
  /**
   * 当月累计PV(人天)
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目简称 */
  projectAlias?: string;
  /** 项目编号 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /**
   * 月工作日(天)
   * @format int32
   */
  workDay?: number;
}

/**
 * ProjectRiskTrackDTO
 * 项目风险DTO
 */
export interface ProjectRiskTrackDTO {
  /**
   * 关闭日期
   * @format date
   */
  closeDate?: string;
  /** 进展描述 */
  description?: string;
  /** @format int64 */
  id: number;
  /** 项目编号 */
  projectCode?: string;
  /** 风险名称 */
  riskName?: string;
  /**
   * 状态
   * @format int32
   */
  riskState?: number;
  /**
   * 风险类型
   * @format int32
   */
  riskType?: number;
}

/**
 * ProjectRiskTrackPagedDTO
 * 项目风险PagedDTO
 */
export interface ProjectRiskTrackPagedDTO {
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 项目编号 */
  projectCode?: string;
}

/**
 * ProjectRiskTrackVO
 * 项目风险VO
 */
export interface ProjectRiskTrackVO {
  /**
   * 关闭日期
   * @format date
   */
  closeDate?: string;
  /** 进展描述 */
  description?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 项目编号 */
  projectCode?: string;
  /** 风险名称 */
  riskName?: string;
  /**
   * 状态
   * @format int32
   */
  riskState?: number;
  /**
   * 风险类型
   * @format int32
   */
  riskType?: number;
}

/**
 * ProjectWeekPlanPagedDTO
 * 项目周计划PagedDTO
 */
export interface ProjectWeekPlanPagedDTO {
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 项目经理工号 */
  jobNumber?: string;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 月计划编号 */
  monthPlanCode?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /**
   * 计划工量(PV-人天)
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /** 周计划编号 */
  weekPlanCode?: string;
  /**
   * 工作天数
   * @format int32
   */
  workDays?: number;
  /**
   * 年份
   * @format int32
   */
  year?: number;
  /**
   * 年度第几周
   * @format int32
   */
  yearWeekNum?: number;
}

/**
 * ProjectWeekPlanVO
 * 项目周计划VO
 */
export interface ProjectWeekPlanVO {
  /**
   * 累计工量(PV-人天)
   * @format bigdecimal
   */
  accumulateWorkLoad?: number;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 月计划编号 */
  monthPlanCode?: string;
  /**
   * 计划工量(PV-人天)
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /** 周计划编号 */
  weekPlanCode?: string;
  /**
   * 工作天数
   * @format int32
   */
  workDays?: number;
  /**
   * 年度第几周
   * @format int32
   */
  yearWeekNum?: number;
}

/**
 * ProjectWeeklyDTO
 * 项目周报DTO
 */
export interface ProjectWeeklyDTO {
  /**
   * 本周EV
   * @format bigdecimal
   */
  currentWeekEv?: number;
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /** 进展描述 */
  description?: string;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /** 填报人 */
  fillInBy?: string;
  /**
   * 填报时间
   * @format date-time
   */
  fillInTime?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 周报明细 */
  items?: WeeklyItems[];
  /** 项目经理（工号） */
  jobNumber?: string;
  /**
   * 上周累计EV
   * @format bigdecimal
   */
  lastWeekTotalEv?: number;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 月计划编号 */
  monthPlanCode?: string;
  /**
   * 进度偏差
   * @format bigdecimal
   */
  paceOffset?: number;
  /**
   * 进度风险
   * @format int32
   */
  paceRisk?: number;
  /** 项目编号 */
  projectCode?: string;
  /** 项目经理 */
  projectManager?: string;
  /** 备注 */
  remark?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /** 所属月 */
  weekMonth?: string;
  /** 周计划编号 */
  weekPlanCode?: string;
  /**
   * 累计EV
   * @format bigdecimal
   */
  weekTotalEv?: number;
  /**
   * 累计PV
   * @format bigdecimal
   */
  weekTotalPv?: number;
  /** 生成周报日期 */
  weeklyDate?: string;
  /**
   * 状态(0-待填写, 1-已填写, 2-已关闭)
   * @format int32
   */
  weeklyState?: number;
  /**
   * 工作天数
   * @format int32
   */
  workDays?: number;
  /**
   * 年份
   * @format int32
   */
  year?: number;
  /**
   * 年度第几周
   * @format int32
   */
  yearWeekNum?: number;
}

/**
 * ProjectWeeklyPagedDTO
 * 项目周报PagedDTO
 */
export interface ProjectWeeklyPagedDTO {
  /**
   * 本周EV
   * @format bigdecimal
   */
  currentWeekEv?: number;
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /** 进展描述 */
  description?: string;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 项目经理（工号） */
  jobNumber?: string;
  /**
   * 上周累计EV
   * @format bigdecimal
   */
  lastWeekTotalEv?: number;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 月计划编号 */
  monthPlanCode?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 进度偏差
   * @format bigdecimal
   */
  paceOffset?: number;
  /**
   * 进度风险
   * @format int32
   */
  paceRisk?: number;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 项目编号 */
  projectCode?: string;
  /** 项目经理 */
  projectManager?: string;
  /** 备注 */
  remark?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /** 跟踪人 */
  trackedBy?: string;
  /**
   * 跟踪时间
   * @format date-time
   */
  trackedTime?: string;
  /** 所属月 */
  weekMonth?: string;
  /** 周计划编号 */
  weekPlanCode?: string;
  /**
   * 累计EV
   * @format bigdecimal
   */
  weekTotalEv?: number;
  /**
   * 累计PV
   * @format bigdecimal
   */
  weekTotalPv?: number;
  /**
   * 状态(1-待填写,2-已填写,3-已关闭)
   * @format int32
   */
  weeklyState?: number;
  /**
   * 年份
   * @format int32
   */
  year?: number;
  /**
   * 年度第几周
   * @format int32
   */
  yearWeekNum?: number;
}

/**
 * ProjectWeeklyParam
 * 项目周报Param
 */
export interface ProjectWeeklyParam {
  dateHandler?: DateHandler;
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /**
   * 周报主键ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 项目经理（工号） */
  jobNumber?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 月计划编号 */
  monthPlanCode?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 项目编号 */
  projectCode?: string;
  /** 跟踪人 */
  trackedBy?: string;
  user?: LoginUser;
  /** 所属月 */
  weekMonth?: string;
  /** 周计划编号 */
  weekPlanCode?: string;
  /** 生成周报日期 */
  weeklyDate?: string;
  /**
   * 状态(1-待填写,2-已填写,3-已关闭)
   * @format int32
   */
  weeklyState?: number;
  /**
   * 年份
   * @format int32
   */
  year?: number;
  /**
   * 年度第几周
   * @format int32
   */
  yearWeekNum?: number;
}

/**
 * ProjectWeeklyVO
 * 项目周报VO
 */
export interface ProjectWeeklyVO {
  /**
   * 本周EV
   * @format bigdecimal
   */
  currentWeekEv?: number;
  /** 周报周期 */
  cycle?: string;
  /**
   * 交付组
   * @format int64
   */
  deliveryTeam?: number;
  /** 进展描述 */
  description?: string;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /** 填报人 */
  fillInBy?: string;
  /**
   * 填报时间
   * @format date-time
   */
  fillInTime?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 周报明细项 */
  items?: WeeklyItemsVO[];
  /** 项目经理（工号） */
  jobNumber?: string;
  /**
   * 上周累计EV
   * @format bigdecimal
   */
  lastWeekTotalEv?: number;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /** 月计划编号 */
  monthPlanCode?: string;
  /**
   * 进度偏差
   * @format bigdecimal
   */
  paceOffset?: number;
  /**
   * 进度风险
   * @format int32
   */
  paceRisk?: number;
  /** 项目编号 */
  projectCode?: string;
  /** 项目经理 */
  projectManager?: string;
  /** 项目名称 */
  projectName?: string;
  /** 备注 */
  remark?: string;
  /**
   * 风险数量
   * @format int32
   */
  riskNum?: number;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /** 所属月 */
  weekMonth?: string;
  /** 周计划编号 */
  weekPlanCode?: string;
  /**
   * 累计EV
   * @format bigdecimal
   */
  weekTotalEv?: number;
  /**
   * 累计PV
   * @format bigdecimal
   */
  weekTotalPv?: number;
  /**
   * 状态(1-待填写,2-已填写,3-已关闭)
   * @format int32
   */
  weeklyState?: number;
  /**
   * 工作天数
   * @format int32
   */
  workDays?: number;
  /**
   * 年份
   * @format int32
   */
  year?: number;
  /**
   * 年度第几周
   * @format int32
   */
  yearWeekNum?: number;
}

/**
 * ProjectYearStatPagedDTO
 * 项目年统计PagedDTO
 */
export interface ProjectYearStatPagedDTO {
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  params?: object;
  /** 项目编号 */
  projectCode?: string;
  /** 项目年度 */
  projectYear?: string;
}

/**
 * ProjectYearStatVO
 * 项目年统计VO
 */
export interface ProjectYearStatVO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 年度计划工量
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /** 项目年度 */
  projectYear?: string;
  /** 备注 */
  remark?: string;
  /**
   * 年度人工单价(元/人年)
   * @format bigdecimal
   */
  yearCostPrice?: number;
  /**
   * 年度计划成本
   * @format bigdecimal
   */
  yearPlanCost?: number;
}

/** SearchDate */
export interface SearchDate {
  keepOriginal?: boolean;
  /** @format date-time */
  ldt?: string;
  /** @format date */
  localDate?: string;
  strDate?: string;
}

/** WeeklyItems */
export interface WeeklyItems {
  createdBy?: string;
  /** @format date-time */
  createdTime?: string;
  /** @format bigdecimal */
  currentWeekEv?: number;
  deleted?: boolean;
  /** @format int64 */
  id?: number;
  /** @format bigdecimal */
  lastWeekTotalEv?: number;
  milestoneCode?: string;
  /** @format bigdecimal */
  paceOffset?: number;
  /** @format int32 */
  paceRisk?: number;
  planCode?: string;
  projectCode?: string;
  repository?: WeeklyItemsRepository;
  updatedBy?: string;
  /** @format date-time */
  updatedTime?: string;
  user?: LoginUser;
  /** @format bigdecimal */
  weekTotalEv?: number;
  /** @format bigdecimal */
  weekTotalPv?: number;
  /** @format int64 */
  weeklyId?: number;
}

/** WeeklyItemsRepository */
export type WeeklyItemsRepository = object;

/**
 * WeeklyItemsVO
 * 周报项VO
 */
export interface WeeklyItemsVO {
  /**
   * 本周EV
   * @format bigdecimal
   */
  currentWeekEv?: number;
  /**
   * 结束日期
   * @format date
   */
  endDate?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 上周累计EV
   * @format bigdecimal
   */
  lastWeekTotalEv?: number;
  /** 里程碑编号 */
  milestoneCode?: string;
  /** 里程碑名称 */
  milestoneName?: string;
  /**
   * 进度偏差
   * @format bigdecimal
   */
  paceOffset?: number;
  /**
   * 进度风险
   * @format int32
   */
  paceRisk?: number;
  /** 月计划编号 */
  planCode?: string;
  /**
   * 计划工量（人天）
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 项目编号 */
  projectCode?: string;
  /**
   * 开始日期
   * @format date
   */
  startDate?: string;
  /**
   * 累计EV
   * @format bigdecimal
   */
  weekTotalEv?: number;
  /**
   * 累计PV
   * @format bigdecimal
   */
  weekTotalPv?: number;
  /**
   * 周报ID
   * @format int64
   */
  weeklyId?: number;
}

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from 'axios';

import { errorConfig, requestConfig, responseConfig } from '../axiosConfig';

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, 'body' | 'method' | 'query' | 'path'>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, 'data' | 'cancelToken'> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: '' });
    this.instance.interceptors.request.use(requestConfig, errorConfig);
    this.instance.interceptors.response.use(responseConfig, errorConfig);
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === 'object' && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] = property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem));
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (type === ContentType.FormData && body && body !== null && typeof body === 'object') {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== 'string') {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type && type !== ContentType.FormData ? { 'Content-Type': type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title SPMP(Swcares Project Manage Platform) Api Doc
 * @version Application Version：3.0.6-RC-250403011712
 * @baseUrl http://**************:8082
 * @contact swcares team <<EMAIL>>
 */
export class Api<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  pmt = {
    /**
     * No description
     *
     * @tags 项目信息接口
     * @name PlmPiDeleteCreate
     * @summary 通过ID删除项目信息记录
     * @request POST:/pmt/plm/pi/delete/{id}
     * @secure
     */
    plmPiDeleteCreate: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/pi/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目信息接口
     * @name PlmPiGetList
     * @summary 通过ID查询项目信息记录
     * @request GET:/pmt/plm/pi/get
     * @secure
     */
    plmPiGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectInfoVO, void>({
        path: `/pmt/plm/pi/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目信息接口
     * @name PlmPiImportDataCreate
     * @summary 导入数据
     * @request POST:/pmt/plm/pi/importData
     * @secure
     */
    plmPiImportDataCreate: (
      data: {
        /**
         * file
         * @format binary
         */
        file: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/pi/importData`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目信息接口
     * @name PlmPiPageCreate
     * @summary 条件分页查询项目信息记录
     * @request POST:/pmt/plm/pi/page
     * @secure
     */
    plmPiPageCreate: (data: ProjectInfoPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectInfoVO, void>({
        path: `/pmt/plm/pi/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目信息接口
     * @name PlmPiSaveCreate
     * @summary 新建项目信息记录
     * @request POST:/pmt/plm/pi/save
     * @secure
     */
    plmPiSaveCreate: (data: ProjectInfoDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/pi/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目信息接口
     * @name PlmPiTemplateCreate
     * @summary 数据模板下载
     * @request POST:/pmt/plm/pi/template
     * @secure
     */
    plmPiTemplateCreate: (params: RequestParams = {}) =>
      this.request<void, void>({
        path: `/pmt/plm/pi/template`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目信息接口
     * @name PlmPiUpdateCreate
     * @summary 修改项目信息记录
     * @request POST:/pmt/plm/pi/update
     * @secure
     */
    plmPiUpdateCreate: (data: ProjectInfoDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/pi/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestoneDeleteCreate
     * @summary 通过ID删除项目里程碑记录
     * @request POST:/pmt/plm/milestone/delete/{id}
     * @secure
     */
    plmMilestoneDeleteCreate: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/milestone/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestoneExportCreate
     * @summary 数据导出
     * @request POST:/pmt/plm/milestone/export
     * @secure
     */
    plmMilestoneExportCreate: (data: ProjectMilestonePagedDTO, params: RequestParams = {}) =>
      this.request<void, void>({
        path: `/pmt/plm/milestone/export`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestoneGetList
     * @summary 通过ID查询项目里程碑记录
     * @request GET:/pmt/plm/milestone/get
     * @secure
     */
    plmMilestoneGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectMilestoneVO, void>({
        path: `/pmt/plm/milestone/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestoneImportDataCreate
     * @summary 导入数据
     * @request POST:/pmt/plm/milestone/importData
     * @secure
     */
    plmMilestoneImportDataCreate: (
      data: {
        /**
         * file
         * @format binary
         */
        file: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/milestone/importData`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestonePageCreate
     * @summary 条件分页查询项目里程碑记录
     * @request POST:/pmt/plm/milestone/page
     * @secure
     */
    plmMilestonePageCreate: (data: ProjectMilestonePagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectMilestoneVO, void>({
        path: `/pmt/plm/milestone/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestoneSaveCreate
     * @summary 新建项目里程碑记录
     * @request POST:/pmt/plm/milestone/save
     * @secure
     */
    plmMilestoneSaveCreate: (data: ProjectMilestoneDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/milestone/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestoneTemplateCreate
     * @summary 数据模板下载
     * @request POST:/pmt/plm/milestone/template
     * @secure
     */
    plmMilestoneTemplateCreate: (params: RequestParams = {}) =>
      this.request<void, void>({
        path: `/pmt/plm/milestone/template`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目里程碑接口
     * @name PlmMilestoneUpdateCreate
     * @summary 修改项目里程碑记录
     * @request POST:/pmt/plm/milestone/update
     * @secure
     */
    plmMilestoneUpdateCreate: (data: ProjectMilestoneDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/milestone/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月成本接口
     * @name PlmCostDeleteCreate
     * @summary 通过ID删除项目月成本记录
     * @request POST:/pmt/plm/cost/delete/{id}
     * @secure
     */
    plmCostDeleteCreate: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/cost/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月成本接口
     * @name PlmCostGetList
     * @summary 通过ID查询项目月成本记录
     * @request GET:/pmt/plm/cost/get
     * @secure
     */
    plmCostGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectMonthCostVO, void>({
        path: `/pmt/plm/cost/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月成本接口
     * @name PlmCostImportDataCreate
     * @summary 导入数据
     * @request POST:/pmt/plm/cost/importData
     * @secure
     */
    plmCostImportDataCreate: (
      data: {
        /**
         * file
         * @format binary
         */
        file: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/cost/importData`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月成本接口
     * @name PlmCostPageCreate
     * @summary 条件分页查询项目月成本记录
     * @request POST:/pmt/plm/cost/page
     * @secure
     */
    plmCostPageCreate: (data: ProjectMonthCostPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectMonthCostVO, void>({
        path: `/pmt/plm/cost/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月成本接口
     * @name PlmCostSaveCreate
     * @summary 新建项目月成本记录
     * @request POST:/pmt/plm/cost/save
     * @secure
     */
    plmCostSaveCreate: (data: ProjectMonthCostDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/cost/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月成本接口
     * @name PlmCostUpdateCreate
     * @summary 修改项目月成本记录
     * @request POST:/pmt/plm/cost/update
     * @secure
     */
    plmCostUpdateCreate: (data: ProjectMonthCostDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/cost/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月进度接口
     * @name PlmPaceDeleteCreate
     * @summary 通过ID删除项目月进度记录
     * @request POST:/pmt/plm/pace/delete/{id}
     * @secure
     */
    plmPaceDeleteCreate: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/pace/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月进度接口
     * @name PlmPaceExportCreate
     * @summary 导出数据
     * @request POST:/pmt/plm/pace/export
     * @secure
     */
    plmPaceExportCreate: (data: ProjectMonthPacePagedDTO, params: RequestParams = {}) =>
      this.request<void, void>({
        path: `/pmt/plm/pace/export`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月进度接口
     * @name PlmPaceGetList
     * @summary 通过ID查询项目月进度记录
     * @request GET:/pmt/plm/pace/get
     * @secure
     */
    plmPaceGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectMonthPaceVO, void>({
        path: `/pmt/plm/pace/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月进度接口
     * @name PlmPacePageCreate
     * @summary 条件分页查询项目月进度记录
     * @request POST:/pmt/plm/pace/page
     * @secure
     */
    plmPacePageCreate: (data: ProjectMonthPacePagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectMonthPaceVO, void>({
        path: `/pmt/plm/pace/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月计划接口
     * @name PlmPlanGetList
     * @summary 通过ID查询项目月计划记录
     * @request GET:/pmt/plm/plan/get
     * @secure
     */
    plmPlanGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectMonthPlanVO, void>({
        path: `/pmt/plm/plan/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目月计划接口
     * @name PlmPlanPageCreate
     * @summary 条件分页查询项目月计划记录
     * @request POST:/pmt/plm/plan/page
     * @secure
     */
    plmPlanPageCreate: (data: ProjectMonthPlanPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectMonthPlanVO, void>({
        path: `/pmt/plm/plan/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目风险接口
     * @name PlmRiskCloseCreate
     * @summary 项目风险关闭
     * @request POST:/pmt/plm/risk/close
     * @secure
     */
    plmRiskCloseCreate: (data: ProjectRiskTrackDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/risk/close`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目风险接口
     * @name PlmRiskDeleteCreate
     * @summary 通过ID删除项目风险记录
     * @request POST:/pmt/plm/risk/delete/{id}
     * @secure
     */
    plmRiskDeleteCreate: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/risk/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目风险接口
     * @name PlmRiskGetList
     * @summary 通过ID查询项目风险记录
     * @request GET:/pmt/plm/risk/get
     * @secure
     */
    plmRiskGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectRiskTrackVO, void>({
        path: `/pmt/plm/risk/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目风险接口
     * @name PlmRiskPageCreate
     * @summary 条件分页查询项目风险记录
     * @request POST:/pmt/plm/risk/page
     * @secure
     */
    plmRiskPageCreate: (data: ProjectRiskTrackPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectRiskTrackVO, void>({
        path: `/pmt/plm/risk/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目风险接口
     * @name PlmRiskSaveCreate
     * @summary 新建项目风险记录
     * @request POST:/pmt/plm/risk/save
     * @secure
     */
    plmRiskSaveCreate: (data: ProjectRiskTrackDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/risk/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目风险接口
     * @name PlmRiskSolveCreate
     * @summary 项目风险解决
     * @request POST:/pmt/plm/risk/solve
     * @secure
     */
    plmRiskSolveCreate: (data: ProjectRiskTrackDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/risk/solve`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周计划接口
     * @name PlmWeekPlanGetList
     * @summary 通过ID查询项目周计划记录
     * @request GET:/pmt/plm/week/plan/get
     * @secure
     */
    plmWeekPlanGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectWeekPlanVO, void>({
        path: `/pmt/plm/week/plan/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周计划接口
     * @name PlmWeekPlanPageCreate
     * @summary 条件分页查询项目周计划记录
     * @request POST:/pmt/plm/week/plan/page
     * @secure
     */
    plmWeekPlanPageCreate: (data: ProjectWeekPlanPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectWeekPlanVO, void>({
        path: `/pmt/plm/week/plan/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周报接口
     * @name PlmWeeklyExportCreate
     * @summary 数据导出
     * @request POST:/pmt/plm/weekly/export
     * @secure
     */
    plmWeeklyExportCreate: (data: ProjectWeeklyPagedDTO, params: RequestParams = {}) =>
      this.request<void, void>({
        path: `/pmt/plm/weekly/export`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周报接口
     * @name PlmWeeklyFillInCreate
     * @summary 填报项目周报
     * @request POST:/pmt/plm/weekly/fill_in
     * @secure
     */
    plmWeeklyFillInCreate: (data: ProjectWeeklyDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/weekly/fill_in`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 可提供参数weeklyDate或year,yearWeekNum生成指定日期的周报，默认生成当前日期所在周的周报。
     *
     * @tags 项目周报接口
     * @name PlmWeeklyGenerateCreate
     * @summary 生成项目周报
     * @request POST:/pmt/plm/weekly/generate
     * @secure
     */
    plmWeeklyGenerateCreate: (data: ProjectWeeklyParam, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/weekly/generate`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周报接口
     * @name PlmWeeklyGetList
     * @summary 通过ID查询项目周报记录
     * @request GET:/pmt/plm/weekly/get
     * @secure
     */
    plmWeeklyGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectWeeklyVO, void>({
        path: `/pmt/plm/weekly/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周报接口
     * @name PlmWeeklyLoadList
     * @summary 填报通过ID加载项目周报，同时校验上周周报填写状态
     * @request GET:/pmt/plm/weekly/load
     * @secure
     */
    plmWeeklyLoadList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectWeeklyVO, void>({
        path: `/pmt/plm/weekly/load`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周报接口
     * @name PlmWeeklyPageCreate
     * @summary 条件分页查询项目周报记录
     * @request POST:/pmt/plm/weekly/page
     * @secure
     */
    plmWeeklyPageCreate: (data: ProjectWeeklyPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectWeeklyVO, void>({
        path: `/pmt/plm/weekly/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目周报接口
     * @name PlmWeeklyTrackCreate
     * @summary 项目周报跟踪
     * @request POST:/pmt/plm/weekly/track
     * @secure
     */
    plmWeeklyTrackCreate: (data: ProjectWeeklyDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/plm/weekly/track`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目年统计接口
     * @name PlmYearStatGetList
     * @summary 通过ID查询项目年统计记录
     * @request GET:/pmt/plm/yearStat/get
     * @secure
     */
    plmYearStatGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectYearStatVO, void>({
        path: `/pmt/plm/yearStat/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 项目年统计接口
     * @name PlmYearStatPageCreate
     * @summary 条件分页查询项目年统计记录
     * @request POST:/pmt/plm/yearStat/page
     * @secure
     */
    plmYearStatPageCreate: (data: ProjectYearStatPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfProjectYearStatVO, void>({
        path: `/pmt/plm/yearStat/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),
  };
}
