/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** BaseResultOfstring */
export interface BaseResultOfstring {
  /** @format int32 */
  code?: number;
  data?: string;
  message?: string;
}

/**
 * H5PayOrderDTO对象
 * H5支付下单
 */
export interface H5PayOrderDTO对象 {
  /** 银行（CCB：建行） */
  bank: 'ABC' | 'CCB';
  /** 回调地址 */
  callbackUrl?: string;
  /** （建行）商户结算账号，央行app使用 */
  cdtrWltId?: string;
  /** 币种；缺省为01－人民币（只支持人民币支付） */
  curCode?: string;
  /** 商户代码 */
  merchantId: string;
  /** 定单号 */
  orderId: string;
  /**
   * 付款金额；单位：分
   * @format bigdecimal
   */
  payAmount: number;
  /** 备注1；作为商户自定义备注信息使用，可在对账单中显示 */
  remark1?: string;
  /** 备注2；作为商户自定义备注信息使用，可在对账单中显示 */
  remark2?: string;
  /**
   * 订单超时时间；YYYY-MM-DD HH:MM:SS
   * @format date-time
   */
  timeout?: string;
}

/**
 * H5PayResultDTO对象
 * H5支付结果查询参数
 */
export interface H5PayResultDTO对象 {
  /** 商户代码 */
  merchantId: string;
  /** 订单编号 */
  orderNo: string;
  /** 状态代码；00-成功，01-失败，02-不确定 */
  transStatus?: string;
}

/**
 * H5PayResultVO对象
 * H5支付结果
 */
export interface H5PayResultVO对象 {
  /** ACCNAME付款户名 */
  accName?: string;
  /** AMOUNT订单金额 */
  amount?: string;
  /** CmAvy_Cntn营销活动名称 */
  cmAvyCntn?: string;
  /** ERRORCODE错误码 */
  errorCode?: string;
  /** ERRORMSG错误信息 */
  errorMsg?: string;
  /** ORDERDATE交易日期；格式：YYYYMMDDHHMMSS */
  orderDate?: string;
  /** ORDERID订单号 */
  orderNo?: string;
  /** Pref_Amt优惠金额 */
  prefAmt?: string;
  /** STATUSCODE订单支付结果；00：成功，中断轮询，01：失败，中断轮询，02：不确定，继续轮询 */
  statusCode?: string;
}

/**
 * MerchantPayChannelConfigVO对象
 * 商户支付渠道配置
 */
export interface MerchantPayChannelConfigVO对象 {
  /** 银行（CCB：建行） */
  bank?: 'ABC' | 'CCB';
  /** （建行）银行公钥 */
  bankPubKey?: string;
  /** （建行）分行代码 */
  branchId?: string;
  /** （建行）调用方ID */
  ftCorpId?: string;
  /** （建行）接口使用场景 */
  ftScenario?: string;
  /**
   * 配置ID
   * @format int64
   */
  id?: number;
  /** 商户代码 */
  merchantId?: string;
  /** （建行）商户私钥 */
  merchantPriKey?: string;
  /** （建行）商户公钥 */
  merchantPubKey?: string;
  /** （建行）操作员密码 */
  password?: string;
  /** （建行）商户柜台代码 */
  posId?: string;
  /** （建行）二级商户代码 */
  subMerchantId?: string;
  /** （建行）操作员号，使用接口前必须使用操作员证书至少登录一次商户服务平台  */
  userId?: string;
}

/** ModelAndView */
export type ModelAndView = object;

/** PagedResultOfListOfH5PayResultVO对象 */
export interface PagedResultOfListOfH5PayResultVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: H5PayResultVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfMerchantPayChannelConfigVO对象 */
export interface PagedResultOfListOfMerchantPayChannelConfigVO对象 {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: MerchantPayChannelConfigVO对象[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from 'axios';

import { errorConfig, requestConfig, responseConfig } from '../axiosConfig';

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  'body' | 'method' | 'query' | 'path'
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, 'data' | 'cancelToken'> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: '' });
    this.instance.interceptors.request.use(requestConfig, errorConfig);
    this.instance.interceptors.response.use(responseConfig, errorConfig);
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === 'object' && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === 'object'
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== 'string'
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type && type !== ContentType.FormData
          ? { 'Content-Type': type }
          : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title ${spring.application.name} Api Doc
 * @version Application Version：0.0.1-SNAPSHOT-@timetest@
 * @baseUrl http://**************:80
 * @contact swcares team <<EMAIL>>
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  dcp = {
    /**
     * No description
     *
     * @tags h5支付
     * @name GetOrderUrlUsingPost
     * @summary 获取下单地址
     * @request POST:/dcp/h5Pay/getOrderUrl
     * @secure
     */
    getOrderUrlUsingPost: (
      data: H5PayOrderDTO对象,
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfstring, void>({
        path: `/dcp/h5Pay/getOrderUrl`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags h5支付
     * @name GetPayResultUsingPost
     * @summary 获取支付结果
     * @request POST:/dcp/h5Pay/getPayResult
     * @secure
     */
    getPayResultUsingPost: (
      data: H5PayResultDTO对象,
      params: RequestParams = {},
    ) =>
      this.request<PagedResultOfListOfH5PayResultVO对象, void>({
        path: `/dcp/h5Pay/getPayResult`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 商户支付渠道配置
     * @name PageUsingPost
     * @summary 条件分页查询
     * @request POST:/dcp/merchantPayChannelConfig/page
     * @secure
     */
    pageUsingPost: (
      data: MerchantPayChannelConfigVO对象,
      params: RequestParams = {},
    ) =>
      this.request<PagedResultOfListOfMerchantPayChannelConfigVO对象, void>({
        path: `/dcp/merchantPayChannelConfig/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingGet
     * @summary getAccessConfirmation
     * @request GET:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingGet: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingPut
     * @summary getAccessConfirmation
     * @request PUT:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingPut: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'PUT',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingPost
     * @summary getAccessConfirmation
     * @request POST:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingPost: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'POST',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingDelete
     * @summary getAccessConfirmation
     * @request DELETE:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingDelete: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'DELETE',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingOptions
     * @summary getAccessConfirmation
     * @request OPTIONS:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingOptions: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'OPTIONS',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingHead
     * @summary getAccessConfirmation
     * @request HEAD:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingHead: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'HEAD',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingPatch
     * @summary getAccessConfirmation
     * @request PATCH:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingPatch: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'PATCH',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags sso-approval-endpoint
     * @name GetAccessConfirmationUsingTrace
     * @summary getAccessConfirmation
     * @request TRACE:/dcp/oauth/confirm_access
     * @secure
     */
    getAccessConfirmationUsingTrace: (
      query?: {
        /** model */
        model?: object;
      },
      params: RequestParams = {},
    ) =>
      this.request<ModelAndView, void>({
        path: `/dcp/oauth/confirm_access`,
        method: 'TRACE',
        query: query,
        secure: true,
        ...params,
      }),
  };
}
