/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/**
 * AirlineInfoVO对象
 * 航空公司信息管理表
 */
export interface AirlineInfoVO对象 {
  /** 航司二字码 */
  airline2code?: string;
  /** 航司三字码 */
  airline3code?: string;
  /** 航司简称 */
  airlineAbbr?: string;
  /** 航司英文名 */
  airlineEn?: string;
  /** 航司名称 */
  airlineName?: string;
  /**
   * 删除状态(1-删除,0-未删除)
   * @example false
   */
  deleted?: boolean;
  /**
   * 服务航班数
   * @format int32
   */
  flightNum?: number;
  /** 航司logo地址 */
  icon?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 是否国内(D-国内,I-国际)【D:国内, I:国际】
   * @format int32
   */
  isDomestic?: number;
  /** 备注 */
  remarks?: string;
  /** 结算代码 */
  settleCode?: string;
}

/** AirportInfoComboVO */
export interface AirportInfoComboVO {
  /** 机场名称 */
  airportName?: string;
  /** 机场名称拼音 */
  airportPinyin?: string;
  /** 城市名称 */
  cityName?: string;
  /** 机场三字码 */
  code?: string;
  /**
   * 是否国内(D-国内,I-国际)【D:国内, I:国际】
   * @format int32
   */
  isDomestic?: number;
}

/**
 * AirportQueryDTO对象
 * 机场信息查询对象
 */
export interface AirportQueryDTO对象 {
  /** 机场三字码集合 */
  airport3CodeList?: string[];
}

/** BaseResultOfCfgCacheVO */
export interface BaseResultOfCfgCacheVO {
  /** @format int32 */
  code?: number;
  /** 参数配置管理数据展示实体对象 */
  data?: CfgCacheVO;
  message?: string;
}

/** BaseResultOfListOfAirlineInfoVO对象 */
export interface BaseResultOfListOfAirlineInfoVO对象 {
  /** @format int32 */
  code?: number;
  data?: AirlineInfoVO对象[];
  message?: string;
}

/** BaseResultOfListOfAirportInfoComboVO */
export interface BaseResultOfListOfAirportInfoComboVO {
  /** @format int32 */
  code?: number;
  data?: AirportInfoComboVO[];
  message?: string;
}

/** BaseResultOfListOfCfgCacheVO */
export interface BaseResultOfListOfCfgCacheVO {
  /** @format int32 */
  code?: number;
  data?: CfgCacheVO[];
  message?: string;
}

/** BaseResultOfListOfDictDataCacheVO */
export interface BaseResultOfListOfDictDataCacheVO {
  /** @format int32 */
  code?: number;
  data?: DictDataCacheVO[];
  message?: string;
}

/** BaseResultOfListOfFeatureResourceTreeVO对象 */
export interface BaseResultOfListOfFeatureResourceTreeVO对象 {
  /** @format int32 */
  code?: number;
  data?: FeatureResourceTreeVO对象[];
  message?: string;
}

/** BaseResultOfListOfOrganizationTreeVO对象 */
export interface BaseResultOfListOfOrganizationTreeVO对象 {
  /** @format int32 */
  code?: number;
  data?: OrganizationTreeVO对象[];
  message?: string;
}

/** BaseResultOfListOfOrganizationVO对象 */
export interface BaseResultOfListOfOrganizationVO对象 {
  /** @format int32 */
  code?: number;
  data?: OrganizationVO对象[];
  message?: string;
}

/** BaseResultOfListOfOrganization对象 */
export interface BaseResultOfListOfOrganization对象 {
  /** @format int32 */
  code?: number;
  data?: Organization对象[];
  message?: string;
}

/** BaseResultOfListOfTenantDataSourceParam */
export interface BaseResultOfListOfTenantDataSourceParam {
  /** @format int32 */
  code?: number;
  data?: TenantDataSourceParam[];
  message?: string;
}

/** BaseResultOfListOfTenantSimplifyVO */
export interface BaseResultOfListOfTenantSimplifyVO {
  /** @format int32 */
  code?: number;
  data?: TenantSimplifyVO[];
  message?: string;
}

/** BaseResultOfListOfUserAllVO用户完整信息 */
export interface BaseResultOfListOfUserAllVO用户完整信息 {
  /** @format int32 */
  code?: number;
  data?: UserAllVO用户完整信息[];
  message?: string;
}

/** BaseResultOfListOflong */
export interface BaseResultOfListOflong {
  /** @format int32 */
  code?: number;
  data?: number[];
  message?: string;
}

/** BaseResultOfListOf行政区划信息返回展示对象(树) */
export interface BaseResultOfListOf行政区划信息返回展示对象树 {
  /** @format int32 */
  code?: number;
  data?: Type行政区划信息返回展示对象树[];
  message?: string;
}

/** BaseResultOfMapOfstringAndListOfDictDataCacheVO */
export interface BaseResultOfMapOfstringAndListOfDictDataCacheVO {
  /** @format int32 */
  code?: number;
  data?: Record<string, DictDataCacheVO[]>;
  message?: string;
}

/** BaseResultOfMapOfstringAndListOf行政区划信息返回展示对象(树) */
export interface BaseResultOfMapOfstringAndListOf行政区划信息返回展示对象树 {
  /** @format int32 */
  code?: number;
  data?: Record<string, Type行政区划信息返回展示对象树[]>;
  message?: string;
}

/** BaseResultOfMapOfstringAndlong */
export interface BaseResultOfMapOfstringAndlong {
  /** @format int32 */
  code?: number;
  data?: Record<string, number>;
  message?: string;
}

/** BaseResultOfMapOfstringAndobject */
export interface BaseResultOfMapOfstringAndobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** BaseResultOfOrganizationVO对象 */
export interface BaseResultOfOrganizationVO对象 {
  /** @format int32 */
  code?: number;
  /** 组织机构，包括根、公司、部门等等 */
  data?: OrganizationVO对象;
  message?: string;
}

/** BaseResultOfTenantSimplifyVO */
export interface BaseResultOfTenantSimplifyVO {
  /** @format int32 */
  code?: number;
  data?: TenantSimplifyVO;
  message?: string;
}

/** BaseResultOfUserVO对象 */
export interface BaseResultOfUserVO对象 {
  /** @format int32 */
  code?: number;
  /** 登录并使用系统的用户 */
  data?: UserVO对象;
  message?: string;
}

/** BaseResultOfobject */
export interface BaseResultOfobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** BaseResultOfstring */
export interface BaseResultOfstring {
  /** @format int32 */
  code?: number;
  data?: string;
  message?: string;
}

/** CaptchaVO */
export interface CaptchaVO {
  /** @format int32 */
  captchaFontSize?: number;
  captchaFontType?: string;
  captchaId?: string;
  captchaOriginalPath?: string;
  captchaType?: string;
  captchaVerification?: string;
  jigsawImageBase64?: string;
  originalImageBase64?: string;
  point?: PointVO;
  pointJson?: string;
  pointList?: Point[];
  projectCode?: string;
  result?: boolean;
  secretKey?: string;
  token?: string;
  wordList?: string[];
}

/**
 * CfgCacheVO
 * 参数配置管理数据展示实体对象
 */
export type CfgCacheVO = object;

/** DictDataCacheVO */
export type DictDataCacheVO = object;

/**
 * DictionaryValueDTO对象
 * 字典修改对象
 */
export interface DictionaryValueDTO对象 {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 字典显示标签 */
  labelOrKey?: string;
  /** 字典值 */
  value?: string;
}

/**
 * EmployeeVO对象
 * 系统所管理的人员
 */
export interface EmployeeVO对象 {
  /**
   * 所属机构的id
   * @format int64
   */
  belongToOrg: number;
  /**
   * 人员的出生日期
   * @format date
   */
  birthday?: string;
  /** 钉钉账号 */
  dingtalk?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 员工的编号 */
  employeeCode?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender: number;
  /** 人员的毕业院校 */
  graduateFrom?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /** 人员的岗位 */
  jobPosition?: string;
  /** 员工的职务 */
  jobTitle?: string;
  /** 人员的姓名 */
  name: string;
  /** 所属机构名称 */
  organizationName?: string;
  /** 人员的电话 */
  phone?: string;
  /** 人员头像的地址 */
  photoRul?: string;
  /** 人员的政治面貌 */
  politicalStatus?: string;
  /** 岗位名称 */
  positionName?: string;
  /** 岗位名称集合 */
  positionNameList?: string[];
  /**
   * 人员当前的状态，例如在职、离职、停职等
   * @format int32
   */
  status?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 工作航站(多个逗号分隔)
   * @example "CTU,PEK"
   */
  workTerminal?: string;
  /** 工作场站名称 */
  workTerminalList?: string[];
  /** 微信OpenId */
  wxOpenid?: string;
}

/**
 * FeatureResourceTreeVO对象
 * 系统资源树返回展示对象
 */
export interface FeatureResourceTreeVO对象 {
  /**
   * 资源是否被选中的状态，区别于资源是否启用的状态和是否被删除的状态
   * @example false
   */
  flag?: boolean;
  /** 资源图标的url */
  icon?: string;
  /**
   * 资源id
   * @format int64
   */
  id?: number;
  /**
   * 是否是公共资源(是否可以被选中)
   * @example false
   */
  isPublic?: boolean;
  /** 资源的名称 */
  name?: string;
  /**
   * 父资源id
   * @format int64
   */
  parentId?: number;
  /** 控件名称 */
  perms?: string;
  /** 资源的前端路由地址 */
  routeUrl?: string;
  /**
   * 资源的排序，代表兄弟资源之间显示的先后顺序
   * @format int32
   */
  sortOrder?: number;
  /** 子系统编码 */
  subsystemCode?: string;
  /**
   * 资源的类型，包括1:子系统、2：菜单、3：操作【1:子系统, 2:菜单, 3:操作】
   * @format int32
   */
  type?: 1 | 2 | 3;
}

/**
 * JobPositionVO对象
 * 岗位信息表
 */
export interface JobPositionVO对象 {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 岗位代码 */
  positionCode?: string;
  /** 岗位名称 */
  positionName?: string;
  /**
   * 岗位类型
   * @format int32
   */
  positionType?: number;
  /** 岗位描述 */
  remark?: string;
}

/**
 * MenuCommonDTO
 * 加载系统资源树通用对象
 */
export interface MenuCommonDTO {
  /**
   * 是否只加载对象已授权的资源,包括1-只加载授权资源、2-包含已授权和未授权资源
   * @format int32
   */
  contain?: 1 | 2;
  /** 对象的id列表，结合idType获取对象已授权的资源.在idType为角色时可以传多个id，其他情况下传1个，不需要加载授权资源时可以不传 */
  idList?: number[];
  /**
   * 对象的id类型，包括0-无、1-客户、2-机构管理员、3-权限组、4-角色
   * @format int32
   */
  idType?: 0 | 1 | 2;
  /**
   * 资源树的范围，包括：0-不限、1-客户、2-机构管理员、3-权限组、4-角色
   * @format int32
   */
  scope?: 0 | 1 | 2 | 3 | 4;
}

/**
 * OrganizationTreeVO对象
 * 组织机构树返回展示对象
 */
export interface OrganizationTreeVO对象 {
  /** 子节点 */
  children?: OrganizationTreeVO对象[];
  /** 机构的编码，例如PSC */
  code?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 当前节点是否存在CA管理员
   * @format int32
   */
  isCA?: number;
  /**
   * 是否一级机构 1是0否
   * @format int32
   */
  isFirst?: number;
  /** 组织机构的全称 */
  name?: string;
  /** 机构的英文名称 */
  nameEn?: string;
  /** 是否可以操作标识0否1是 */
  option?: string;
  /**
   * 父机构ID
   * @format int64
   */
  parentId?: number;
  /** 机构的代码，由代码生成 */
  serialNo?: string;
  /** 机构名称的简称 */
  shortName?: string;
  /**
   * 排序号
   * @format int32
   */
  sortOrder?: number;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/**
 * OrganizationVO对象
 * 组织机构，包括根、公司、部门等等
 */
export interface OrganizationVO对象 {
  /**
   * 机构的地址
   * @minLength 0
   * @maxLength 32
   */
  address?: string;
  /**
   * 机构所从事的业务的类别
   * @format int64
   */
  businessType?: number;
  /** 机构子节点 */
  child?: OrganizationVO对象[];
  /**
   * 机构的编码，例如PSC
   * @minLength 0
   * @maxLength 8
   */
  code?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 机构所属的行业
   * @format int64
   */
  industryType?: number;
  /**
   * 机构的简介
   * @minLength 0
   * @maxLength 65
   */
  introduction?: string;
  /**
   * 组织机构的全称
   * @minLength 0
   * @maxLength 21
   */
  name?: string;
  /**
   * 机构的英文名称
   * @minLength 0
   * @maxLength 64
   */
  nameEn?: string;
  /** name全路径 */
  nameFullPath?: string;
  /**
   * 父机构ID
   * @format int64
   */
  parentId?: number;
  /**
   * 人数
   * @format int32
   */
  peopleNum?: number;
  /**
   * 机构的负责人
   * @minLength 0
   * @maxLength 20
   */
  personInCharge?: string;
  /**
   * 机构的电话
   * @minLength 0
   * @maxLength 20
   */
  phone?: string;
  /**
   * 机构的代码，由代码生成
   * @minLength 0
   * @maxLength 32
   */
  serialNo?: string;
  /**
   * 机构名称的简称
   * @minLength 0
   * @maxLength 16
   */
  shortName?: string;
  /**
   * 排序号
   * @format int32
   */
  sortOrder?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/**
 * Organization对象
 * 组织机构，包括根、公司、部门等等
 */
export interface Organization对象 {
  /**
   * 机构的地址
   * @minLength 0
   * @maxLength 32
   */
  address?: string;
  /**
   * 机构所从事的业务的类别
   * @format int64
   */
  businessType?: number;
  /**
   * 机构的编码，例如PSC
   * @minLength 0
   * @maxLength 8
   */
  code?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 机构所属的行业
   * @format int64
   */
  industryType?: number;
  /**
   * 机构的简介
   * @minLength 0
   * @maxLength 65
   */
  introduction?: string;
  /**
   * 组织机构的全称
   * @minLength 0
   * @maxLength 21
   */
  name?: string;
  /**
   * 机构的英文名称
   * @minLength 0
   * @maxLength 64
   */
  nameEn?: string;
  /** name全路径 */
  nameFullPath?: string;
  /**
   * 父机构ID
   * @format int64
   */
  parentId?: number;
  /**
   * 机构的负责人
   * @minLength 0
   * @maxLength 20
   */
  personInCharge?: string;
  /**
   * 机构的电话
   * @minLength 0
   * @maxLength 20
   */
  phone?: string;
  /**
   * 机构的代码，由代码生成
   * @minLength 0
   * @maxLength 32
   */
  serialNo?: string;
  /**
   * 机构名称的简称
   * @minLength 0
   * @maxLength 16
   */
  shortName?: string;
  /**
   * 排序号
   * @format int32
   */
  sortOrder?: number;
  /**
   * 钉钉同步状态(0-未同步、1-同步成功、2-同步失败、3-已从钉钉删除)
   * @format int32
   */
  syncStatus?: number;
  /**
   * 机构的类型
   * @format int32
   */
  type?: number;
}

/** Point */
export interface Point {
  /** @format int32 */
  x?: number;
  /** @format int32 */
  y?: number;
}

/** PointVO */
export interface PointVO {
  secretKey?: string;
  /** @format int32 */
  x?: number;
  /** @format int32 */
  y?: number;
}

/** TenantDataSourceParam */
export type TenantDataSourceParam = object;

/** TenantSimplifyVO */
export interface TenantSimplifyVO {
  /** 租户公司的全称 */
  companyName?: string;
  /** 分配给租户的域名，使用该域名来区分租户 */
  domainName?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 用户编码 */
  tenantCode?: string;
  /** 租户的名称 */
  tenantName?: string;
  /** 客户企业管理员账号的用户名 */
  userName?: string;
}

/**
 * TypeDataDTO对象
 * 字典修改对象
 */
export interface TypeDataDTO对象 {
  /** 类型 */
  type?: string;
  /** 数据 */
  values?: DictionaryValueDTO对象[];
}

/**
 * UserAllVO用户完整信息
 * 用户完整信息
 */
export interface UserAllVO用户完整信息 {
  /** 用户的头像图片url */
  avatar?: string;
  /** 用户对应的员工对象 */
  employee?: EmployeeVO对象;
  /**
   * 人员的唯一标识，没有业务含义
   * @format int64
   */
  employeeId?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
  /** 用户对应的员工的部门对象 */
  organization?: OrganizationVO对象;
  /**
   * 用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】
   * @format int32
   */
  passwordStatus?: 1 | 2;
  /** 备注 */
  remark?: string;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /**
   * null【1:上班, 2:值班, 3:休假】
   * @format int32
   */
  workStatus?: 1 | 2 | 3;
}

/**
 * UserVO对象
 * 登录并使用系统的用户
 */
export interface UserVO对象 {
  /** 用户的头像图片url */
  avatar?: string;
  /** 人员的电子邮件地址 */
  emailAddress?: string;
  /** 人员编码 */
  employeeCode?: string;
  /**
   * 人员的唯一标识，没有业务含义
   * @format int64
   */
  employeeId?: number;
  /** 人员姓名 */
  employeeName?: string;
  /**
   * 人员的性别，1表示男性，2表示女性
   * @format int32
   */
  gender?: number;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 身份证号 */
  idCard?: string;
  /** 人员工号 */
  jobNumber?: string;
  /**
   * 岗位代码(多个逗号分隔)
   * @example "0001,01AB"
   */
  jobPosition?: string;
  /** 用户名，可用于登录系统，必须唯一。 */
  name?: string;
  /** 归属机构全路径 */
  nameFullPath?: string;
  /**
   * 归属机构ID
   * @format int64
   */
  organizationId?: number;
  /** 归属机构 */
  organizationName?: string;
  /**
   * 归属机构ID【0:集团, 1:公司, 2:部门】
   * @format int32
   */
  organizationType?: 0 | 1 | 2;
  /**
   * 用户的密码状态，1标识是初始状态，需要修改密码【1:初始化, 2:已修改】
   * @format int32
   */
  passwordStatus?: 1 | 2;
  /** 从根节点到当前节点的路径(不包含当前节点)， 例如(/r-1/c-13/d-25).  由代码自动生成，不展示给用户。用于后端查询或者生成树的时候使用 */
  path?: string;
  /** 人员的电话 */
  phone?: string;
  /** 岗位集合 */
  positionList?: JobPositionVO对象[];
  /** 备注 */
  remark?: string;
  /**
   * 用户的状态，例如0：停用，1：启用，2：锁定，3：过期等【1:启用, 0:停用, 2:锁定, 3:过期, 4:未启用】
   * @format int32
   */
  status?: 0 | 1 | 2 | 3 | 4;
  /**
   * 用户的类型，-1-系统管理员，1-企业管理员，2-普通管理员，3-普通用户，4-机构管理员【-1:系统管理员, 1:企业管理员, 2:普通管理员, 3:普通用户, 4:机构管理员】
   * @format int32
   */
  type?: -1 | 1 | 2 | 3 | 4;
  /**
   * null【1:上班, 2:值班, 3:休假】
   * @format int32
   */
  workStatus?: 1 | 2 | 3;
  /**
   * 工作航站(多个逗号分隔)
   * @example "CTU,PEK"
   */
  workTerminal?: string;
  /** 微信OpenId */
  wxOpenid?: string;
}

/** 绑定微信DTO */
export interface Type绑定微信DTO {
  /** 身份证号 */
  idCard: string;
  /** 人员的姓名 */
  name: string;
  /** 微信unionId */
  unionId: string;
}

/** 获取行政区域列表DTO */
export interface Type获取行政区域列表DTO {
  /**
   * 取国家级行政区域时，是否包含中国
   * @example false
   */
  containsChina?: boolean;
  /** 父级资源编码，id>0正常取子级，id=0取国家级，id=-1取国内省 */
  parentCode?: string;
}

/**
 * 行政区划信息返回展示对象(树)
 * 行政区划信息
 */
export interface Type行政区划信息返回展示对象树 {
  /** 区域编码 */
  areaCode?: string;
  /** 区域名称 */
  areaName?: string;
  /** 区域类型(1-国家,2-省,3-城市,4-行政区) */
  areaType?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 父级区域编码 */
  parentCode?: string;
  /**
   * 排序号
   * @format int32
   */
  sortNum?: number;
}

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from 'axios';

import { errorConfig, requestConfig, responseConfig } from '../axiosConfig';

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, 'body' | 'method' | 'query' | 'path'>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, 'data' | 'cancelToken'> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: '' });
    this.instance.interceptors.request.use(requestConfig, errorConfig);
    this.instance.interceptors.response.use(responseConfig, errorConfig);
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === 'object' && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] = property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem));
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (type === ContentType.FormData && body && body !== null && typeof body === 'object') {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== 'string') {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type && type !== ContentType.FormData ? { 'Content-Type': type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title SPMP(Swcares Project Manage Platform) Api Doc
 * @version Application Version：3.0.6-RC-250403011712
 * @baseUrl http://**************:8082
 * @contact swcares team <<EMAIL>>
 */
export class Api<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  pmt = {
    /**
     * No description
     *
     * @tags 基础数据公共访问接口
     * @name CommonBdAirlineComboList
     * @summary 航司信息下拉框框数据源接口
     * @request GET:/pmt/common/bd/airline/combo
     * @secure
     */
    commonBdAirlineComboList: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfAirlineInfoVO对象, void>({
        path: `/pmt/common/bd/airline/combo`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 基础数据公共访问接口
     * @name CommonBdAirportComboList
     * @summary 机场信息下拉框框数据源接口
     * @request GET:/pmt/common/bd/airport/combo
     * @secure
     */
    commonBdAirportComboList: (
      query?: {
        /** param */
        param?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfAirportInfoComboVO, void>({
        path: `/pmt/common/bd/airport/combo`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 基础数据公共访问接口
     * @name CommonBdAirportListCreate
     * @summary 条件查询机场信息列表接口
     * @request POST:/pmt/common/bd/airport/list
     * @secure
     */
    commonBdAirportListCreate: (data: AirportQueryDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfAirportInfoComboVO, void>({
        path: `/pmt/common/bd/airport/list`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 基础数据公共访问接口
     * @name CommonBdComboTreeList
     * @summary 加载已有启用状态数据，并返回树型结构返回，提供模糊搜索
     * @request GET:/pmt/common/bd/combo_tree
     * @secure
     */
    commonBdComboTreeList: (
      query: {
        /** areaName */
        areaName: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOf行政区划信息返回展示对象树, void>({
        path: `/pmt/common/bd/combo_tree`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 基础数据公共访问接口
     * @name CommonBdGetAllList
     * @summary 获取国内和国外完整行政区域树状结构,type空：省市，type=4：省市县
     * @request GET:/pmt/common/bd/get_all
     * @secure
     */
    commonBdGetAllList: (
      query?: {
        /**
         * type
         * @format int64
         */
        type?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfMapOfstringAndListOf行政区划信息返回展示对象树, void>({
        path: `/pmt/common/bd/get_all`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 基础数据公共访问接口
     * @name CommonBdGetByPcodeCreate
     * @summary 根据父级区域编码，获取该父级的下一级行政区划，id>0正常取子级，id=0取所有国外，id=-1取国内省
     * @request POST:/pmt/common/bd/get_by_pcode
     * @secure
     */
    commonBdGetByPcodeCreate: (data: Type获取行政区域列表DTO, params: RequestParams = {}) =>
      this.request<BaseResultOfListOf行政区划信息返回展示对象树, void>({
        path: `/pmt/common/bd/get_by_pcode`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 必填参数captchaType、token、captchaVerification
     *
     * @tags 验证码接口
     * @name CaptchaCheckCreate
     * @summary 验证码校验接口
     * @request POST:/pmt/captcha/check
     * @secure
     */
    captchaCheckCreate: (data: CaptchaVO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/captcha/check`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 必填参数captchaType，值可选范围【clickWord-点选验证码, blockPuzzle-滑块验证码, bladePatchca-图片字符验证码】
     *
     * @tags 验证码接口
     * @name CaptchaGetCreate
     * @summary 验证码图片获取接口
     * @request POST:/pmt/captcha/get
     * @secure
     */
    captchaGetCreate: (data: CaptchaVO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/captcha/get`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 必填参数token、captchaVerification
     *
     * @tags 验证码接口
     * @name CaptchaVerifCreate
     * @summary 二次验证接口，后台使用。
     * @request POST:/pmt/captcha/verif
     * @secure
     */
    captchaVerifCreate: (data: CaptchaVO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/captcha/verif`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description 通过参数配置键(Key)获取配置参数值对象
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigGetConfigList
     * @summary 系统参数配置远程调用接口
     * @request GET:/pmt/common/config/get_config
     * @secure
     */
    commonConfigGetConfigList: (
      query?: {
        /** configKey */
        configKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfCfgCacheVO, void>({
        path: `/pmt/common/config/get_config`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * @description 通过参数配置键(Key)获取配置参数值对象
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigGetConfigDbList
     * @summary 系统参数配置远程调用接口
     * @request GET:/pmt/common/config/get_config_db
     * @secure
     */
    commonConfigGetConfigDbList: (
      query?: {
        /** configKey */
        configKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfCfgCacheVO, void>({
        path: `/pmt/common/config/get_config_db`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * @description 通过参数配置键(Key)获取配置参数值对象，只获取开放前端的配置参数
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigGetFrontCfgList
     * @summary 配置参数前端调用接口
     * @request GET:/pmt/common/config/get_front_cfg
     * @secure
     */
    commonConfigGetFrontCfgList: (
      query?: {
        /** configKey */
        configKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfCfgCacheVO, void>({
        path: `/pmt/common/config/get_front_cfg`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * @description 通过参数配置键(Key)获取配置参数值对象，获取所有开放前端的配置参数
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigGetFrontCfgsList
     * @summary 配置参数前端调用接口
     * @request GET:/pmt/common/config/get_front_cfgs
     * @secure
     */
    commonConfigGetFrontCfgsList: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfCfgCacheVO, void>({
        path: `/pmt/common/config/get_front_cfgs`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * @description 通过参数配置键(Key)获取配置参数值，返回String类型的数值
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigGetValueList
     * @summary 系统参数配置远程调用接口
     * @request GET:/pmt/common/config/get_value
     * @secure
     */
    commonConfigGetValueList: (
      query?: {
        /** configKey */
        configKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfstring, void>({
        path: `/pmt/common/config/get_value`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * @description 通过参数类型键(Key)和数值键来获取配置参数值，返回String类型的数值
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigGetValueByKeyList
     * @summary 系统参数配置远程调用接口
     * @request GET:/pmt/common/config/get_value_by_key
     * @secure
     */
    commonConfigGetValueByKeyList: (
      query?: {
        /** cfgKey */
        cfgKey?: string;
        /** valKey */
        valKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfstring, void>({
        path: `/pmt/common/config/get_value_by_key`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigModifyCreate
     * @summary 编辑保存配置(增改)
     * @request POST:/pmt/common/config/modify
     * @secure
     */
    commonConfigModifyCreate: (data: TypeDataDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/common/config/modify`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 配置信息公共通用访问接口
     * @name CommonConfigRefreshList
     * @summary 刷新缓存
     * @request GET:/pmt/common/config/refresh
     * @secure
     */
    commonConfigRefreshList: (params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/common/config/refresh`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 参数配置服务接口
     * @name LoginGetLoginCfgList
     * @summary getLoginCfg
     * @request GET:/pmt/login/get_login_cfg
     * @secure
     */
    loginGetLoginCfgList: (params: RequestParams = {}) =>
      this.request<BaseResultOfMapOfstringAndobject, void>({
        path: `/pmt/login/get_login_cfg`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 参数配置服务接口
     * @name LoginGetSyncModeList
     * @summary getSyncMode
     * @request GET:/pmt/login/get_sync_mode
     * @secure
     */
    loginGetSyncModeList: (params: RequestParams = {}) =>
      this.request<BaseResultOfstring, void>({
        path: `/pmt/login/get_sync_mode`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * @description 根据字典类型，获取此字典类型下状态为启用的数据;
     *
     * @tags 字典公共访问接口
     * @name CommonDictGetList
     * @summary 字典数据获取通用接口，URL传参
     * @request GET:/pmt/common/dict/get
     * @secure
     */
    commonDictGetList: (
      query: {
        /** 字典类型 */
        dictType: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfDictDataCacheVO, void>({
        path: `/pmt/common/dict/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * @description 根据字典类型，获取此字典类型下状态为启用的数据;
     *
     * @tags 字典公共访问接口
     * @name CommonDictGetDetail
     * @summary 字典数据获取通用接口
     * @request GET:/pmt/common/dict/get/{dictType}
     * @secure
     */
    commonDictGetDetail: (dictType: string, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfDictDataCacheVO, void>({
        path: `/pmt/common/dict/get/${dictType}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 字典公共访问接口
     * @name CommonDictGetAllList
     * @summary 加载全部数据字典
     * @request GET:/pmt/common/dict/get_all
     * @secure
     */
    commonDictGetAllList: (params: RequestParams = {}) =>
      this.request<BaseResultOfMapOfstringAndListOfDictDataCacheVO, void>({
        path: `/pmt/common/dict/get_all`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 字典公共访问接口
     * @name CommonDictModifyCreate
     * @summary 编辑保存选项(增删改)
     * @request POST:/pmt/common/dict/modify
     * @secure
     */
    commonDictModifyCreate: (data: TypeDataDTO对象, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/common/dict/modify`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 字典公共访问接口
     * @name CommonDictRefreshList
     * @summary 刷新缓存
     * @request GET:/pmt/common/dict/refresh
     * @secure
     */
    commonDictRefreshList: (params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/common/dict/refresh`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 菜单资源公共接口
     * @name CommonMenuGetAtreeCreate
     * @summary 完整的菜单资源树
     * @request POST:/pmt/common/menu/get_atree
     * @secure
     */
    commonMenuGetAtreeCreate: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfFeatureResourceTreeVO对象, void>({
        path: `/pmt/common/menu/get_atree`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 菜单资源公共接口
     * @name CommonMenuGetByScopeCreate
     * @summary 根据指定范围加载菜单资源树
     * @request POST:/pmt/common/menu/get_by_scope
     * @secure
     */
    commonMenuGetByScopeCreate: (data: MenuCommonDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfListOfFeatureResourceTreeVO对象, void>({
        path: `/pmt/common/menu/get_by_scope`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构公共接口
     * @name CommonOrgAllList
     * @summary 查询所有机构
     * @request GET:/pmt/common/org/all
     * @secure
     */
    commonOrgAllList: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfOrganizationVO对象, void>({
        path: `/pmt/common/org/all`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构公共接口
     * @name CommonOrgGetList
     * @summary 通过ID查询组织机构，包括根、公司、部门等等记录，URL传参
     * @request GET:/pmt/common/org/get
     * @secure
     */
    commonOrgGetList: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfOrganizationVO对象, void>({
        path: `/pmt/common/org/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构公共接口
     * @name CommonOrgGetAllChildList
     * @summary 根据指定父级获取所有子级
     * @request GET:/pmt/common/org/get_all_child
     * @secure
     */
    commonOrgGetAllChildList: (
      query: {
        /**
         * ids
         * @format int64
         */
        ids: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfOrganizationVO对象, void>({
        path: `/pmt/common/org/get_all_child`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构公共接口
     * @name CommonOrgGetAllChildIdList
     * @summary 根据指定父级获取所有子级机构ID
     * @request GET:/pmt/common/org/get_all_child_id
     * @secure
     */
    commonOrgGetAllChildIdList: (
      query: {
        /**
         * ids
         * @format int64
         */
        ids: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOflong, void>({
        path: `/pmt/common/org/get_all_child_id`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构公共接口
     * @name CommonOrgGetByIdsCreate
     * @summary 根据机构ID集，获取对应的机构对象集, 一次参数最大个数为999
     * @request POST:/pmt/common/org/get_by_ids
     * @secure
     */
    commonOrgGetByIdsCreate: (data: number[], params: RequestParams = {}) =>
      this.request<BaseResultOfListOfOrganization对象, void>({
        path: `/pmt/common/org/get_by_ids`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * @description isScope-是否指定范围，1-管理、0-全部
     *
     * @tags 组织机构公共接口
     * @name CommonOrgGetOneList
     * @summary 根据指定范围加载一级机构
     * @request GET:/pmt/common/org/get_one
     * @deprecated
     * @secure
     */
    commonOrgGetOneList: (
      query?: {
        /**
         * isScope
         * @format int32
         */
        isScope?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfOrganizationVO对象, void>({
        path: `/pmt/common/org/get_one`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构公共接口
     * @name CommonOrgGetOrgTreeList
     * @summary 根据指定条件获取机构树
     * @request GET:/pmt/common/org/get_org_tree
     * @secure
     */
    commonOrgGetOrgTreeList: (
      query?: {
        /**
         * 机构id
         * @format int64
         */
        orgId?: number;
        /**
         * 是否获取用户的管理机构:0全部1管理机构2工作机构
         * @format int32
         */
        orgType?: 0 | 1 | 2;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfOrganizationTreeVO对象, void>({
        path: `/pmt/common/org/get_org_tree`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 组织机构公共接口
     * @name CommonOrgNextOneLevelChildrenList
     * @summary 通过父ID，查下一级机构
     * @request GET:/pmt/common/org/next_one_level_children
     * @secure
     */
    commonOrgNextOneLevelChildrenList: (
      query: {
        /**
         * ids
         * @format int64
         */
        ids: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfOrganizationTreeVO对象, void>({
        path: `/pmt/common/org/next_one_level_children`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 租户相关共用接口
     * @name LoginTenantDatasourceList
     * @summary 获取子系统数据源
     * @request GET:/pmt/login/tenant/datasource
     * @secure
     */
    loginTenantDatasourceList: (
      query?: {
        /** subSystemCode */
        subSystemCode?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfTenantDataSourceParam, void>({
        path: `/pmt/login/tenant/datasource`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 租户相关共用接口
     * @name LoginTenantDomainList
     * @summary 获取所有域名
     * @request GET:/pmt/login/tenant/domain
     * @secure
     */
    loginTenantDomainList: (params: RequestParams = {}) =>
      this.request<BaseResultOfMapOfstringAndlong, void>({
        path: `/pmt/login/tenant/domain`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 租户相关共用接口
     * @name LoginTenantGetTenantDetail
     * @summary 获取租户ID获取租户信息
     * @request GET:/pmt/login/tenant/get_tenant/{id}
     * @secure
     */
    loginTenantGetTenantDetail: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfTenantSimplifyVO, void>({
        path: `/pmt/login/tenant/get_tenant/${id}`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 租户相关共用接口
     * @name LoginTenantTenantsList
     * @summary 获取所有有效租户
     * @request GET:/pmt/login/tenant/tenants
     * @secure
     */
    loginTenantTenantsList: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfTenantSimplifyVO, void>({
        path: `/pmt/login/tenant/tenants`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户公共接口
     * @name CommonUserGetByIdsCreate
     * @summary 根据用户ID，获取用户对象，一次最多支持（999）个
     * @request POST:/pmt/common/user/get_by_ids
     * @secure
     */
    commonUserGetByIdsCreate: (data: number[], params: RequestParams = {}) =>
      this.request<BaseResultOfListOfUserAllVO用户完整信息, void>({
        path: `/pmt/common/user/get_by_ids`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户公共接口
     * @name CommonUserGetWorkTerminalList
     * @summary 获取当前用户工作航站的combo数据，来源于人员管理的工作场站
     * @request GET:/pmt/common/user/get_work_terminal
     * @secure
     */
    commonUserGetWorkTerminalList: (params: RequestParams = {}) =>
      this.request<BaseResultOfListOfAirportInfoComboVO, void>({
        path: `/pmt/common/user/get_work_terminal`,
        method: 'GET',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户公共接口
     * @name CommonUserWechatBindCreate
     * @summary 绑定微信身份证--只需要客户端模式登录
     * @request POST:/pmt/common/user/wechatBind
     * @secure
     */
    commonUserWechatBindCreate: (data: Type绑定微信DTO, params: RequestParams = {}) =>
      this.request<BaseResultOfUserVO对象, void>({
        path: `/pmt/common/user/wechatBind`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 用户公共接口
     * @name CommonUserWechatBindSelfCreate
     * @summary 绑定微信身份证--保证用户先通过其它方式登录，再传unionId绑定
     * @request POST:/pmt/common/user/wechatBindSelf
     * @secure
     */
    commonUserWechatBindSelfCreate: (
      query: {
        /** unionId */
        unionId: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfUserVO对象, void>({
        path: `/pmt/common/user/wechatBindSelf`,
        method: 'POST',
        query: query,
        secure: true,
        ...params,
      }),
  };
}
