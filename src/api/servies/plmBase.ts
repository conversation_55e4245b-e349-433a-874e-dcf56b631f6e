/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** BaseResultOfStaffJobLevelVO */
export interface BaseResultOfStaffJobLevelVO {
  /** @format int32 */
  code?: number;
  /** 资源岗级 */
  data?: StaffJobLevelVO;
  message?: string;
}

/** BaseResultOfStaffResources */
export interface BaseResultOfStaffResources {
  /** @format int32 */
  code?: number;
  /** 人力资源 */
  data?: StaffResources;
  message?: string;
}

/** BaseResultOfStaffWorkLoadVO */
export interface BaseResultOfStaffWorkLoadVO {
  /** @format int32 */
  code?: number;
  /** 资源使用记录 */
  data?: StaffWorkLoadVO;
  message?: string;
}

/** BaseResultOfStaffWorkStateVO */
export interface BaseResultOfStaffWorkStateVO {
  /** @format int32 */
  code?: number;
  /** 在职状态 */
  data?: StaffWorkStateVO;
  message?: string;
}

/** BaseResultOfobject */
export interface BaseResultOfobject {
  /** @format int32 */
  code?: number;
  data?: object;
  message?: string;
}

/** OrderItem */
export interface OrderItem {
  asc?: boolean;
  column?: string;
}

/** PagedResultOfListOfStaffJobLevelVO */
export interface PagedResultOfListOfStaffJobLevelVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: StaffJobLevelVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfStaffResources */
export interface PagedResultOfListOfStaffResources {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: StaffResources[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfStaffWorkLoadVO */
export interface PagedResultOfListOfStaffWorkLoadVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: StaffWorkLoadVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/** PagedResultOfListOfStaffWorkStateVO */
export interface PagedResultOfListOfStaffWorkStateVO {
  /** @format int32 */
  code?: number;
  /**
   * 当前是第几页的数据
   * @format int64
   */
  currentPage?: number;
  /** 当前页包含的数据内容 */
  data?: StaffWorkStateVO[];
  message?: string;
  /**
   * 每页包含的数据条数
   * @format int64
   */
  pageSize?: number;
  /**
   * 总共有多少页数据
   * @format int64
   */
  totalPages?: number;
  /**
   * 总共有多少条数据
   * @format int64
   */
  totalRecords?: number;
}

/**
 * StaffJobLevelDTO
 * 资源岗级DTO
 */
export interface StaffJobLevelDTO {
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /**
   * 岗位ID
   * @format int64
   */
  jobId?: number;
  /**
   * 岗级ID
   * @format int64
   */
  jobLevelId?: number;
  /** 岗级名称 */
  jobLevelName?: string;
  /** 岗位名称 */
  jobName?: string;
  /**
   * 人数
   * @format int32
   */
  staffNumber?: number;
}

/**
 * StaffJobLevelPagedDTO
 * 资源岗级PagedDTO
 */
export interface StaffJobLevelPagedDTO {
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /**
   * 岗位ID
   * @format int64
   */
  jobId?: number;
  /**
   * 岗级ID
   * @format int64
   */
  jobLevelId?: number;
  /** 岗级名称 */
  jobLevelName?: string;
  /** 岗位名称 */
  jobName?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
}

/**
 * StaffJobLevelVO
 * 资源岗级
 */
export interface StaffJobLevelVO {
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /**
   * 岗位ID
   * @format int64
   */
  jobId?: number;
  /**
   * 岗级ID
   * @format int64
   */
  jobLevelId?: number;
  /** 岗级名称 */
  jobLevelName?: string;
  /** 岗位名称 */
  jobName?: string;
  /**
   * 人数
   * @format int32
   */
  staffNumber?: number;
}

/**
 * StaffResources
 * 人力资源
 */
export interface StaffResources {
  /**
   * 离职日期
   * @format date
   */
  departDate?: string;
  /**
   * 入职日期
   * @format date
   */
  entryDate?: string;
  /**
   * ID
   * @format int64
   */
  id?: number;
  /**
   * 岗位ID
   * @format int64
   */
  jobId?: number;
  /** 岗位名称 */
  jobName?: string;
  /** 工号 */
  jobNumber?: string;
  /** 姓名 */
  realName?: string;
  /** 备注 */
  remark?: string;
  /**
   * 状态
   * @format int32
   */
  staffState?: number;
  /**
   * 团队ID
   * @format int64
   */
  teamId?: number;
  /** 团队名称 */
  teamName?: string;
}

/**
 * StaffResourcesDTO
 * 人力资源DTO
 */
export interface StaffResourcesDTO {
  /**
   * 离职日期
   * @format date
   */
  departDate?: string;
  /**
   * 入职日期
   * @format date
   */
  entryDate?: string;
  /**
   * id
   * @format int64
   */
  id?: number;
  /**
   * 岗位ID
   * @format int64
   */
  jobId?: number;
  /** 岗位 */
  jobName?: string;
  /** 工号 */
  jobNumber?: string;
  /** 姓名 */
  realName?: string;
  /** 备注 */
  remark?: string;
  /**
   * 在职状态
   * @format int64
   */
  staffState?: number;
  /** 在职状态 */
  staffStateName?: string;
  /**
   * 团队ID
   * @format int64
   */
  teamId?: number;
  /** 所属交付组 */
  teamName?: string;
}

/**
 * StaffResourcesPagedDTO
 * 人力资源PagedDTO
 */
export interface StaffResourcesPagedDTO {
  /**
   * 离职日期
   * @format date
   */
  departDate?: string;
  /**
   * 入职日期
   * @format date
   */
  entryDate?: string;
  /**
   * ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /**
   * 岗位ID
   * @format int64
   */
  jobId?: number;
  /** 工号 */
  jobNumber?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  /** 姓名 */
  realName?: string;
  /** 备注 */
  remark?: string;
  /**
   * 状态
   * @format int64
   */
  staffState?: number;
  /**
   * 团队ID
   * @format int64
   */
  teamId?: number;
}

/**
 * StaffWorkLoadDTO
 * 资源使用记录
 */
export interface StaffWorkLoadDTO {
  /**
   * 使用度
   * @format int32
   */
  capacity?: number;
  /**
   * 数据类型(1-负载，2-投入)
   * @format int32
   */
  dataType?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 工号 */
  jobNumber?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /** 项目年 */
  projectYear?: string;
  /** 年月 */
  yearMonths?: string;
}

/**
 * StaffWorkLoadPagedDTO
 * 资源使用记录
 */
export interface StaffWorkLoadPagedDTO {
  /**
   * 数据类型(1-负载，2-投入)
   * @format int32
   */
  dataType?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 工号 */
  jobNumber?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /** 年度 */
  projectYear?: string;
  /** 年月 */
  yearMonths?: string;
}

/**
 * StaffWorkLoadVO
 * 资源使用记录
 */
export interface StaffWorkLoadVO {
  /**
   * 使用度
   * @format int32
   */
  capacity?: number;
  /**
   * 数据类型(1-负载，2-投入)
   * @format int32
   */
  dataType?: number;
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 工号 */
  jobNumber?: string;
  /** 项目编码 */
  projectCode?: string;
  /** 项目名称 */
  projectName?: string;
  /** 项目年 */
  projectYear?: string;
  /** 年月 */
  yearMonths?: string;
}

/**
 * StaffWorkStateDTO
 * 在职状态
 */
export interface StaffWorkStateDTO {
  /**
   * id
   * @format int64
   */
  id?: number;
  /** 岗位 */
  jobName?: string;
  /** 工号 */
  jobNumber?: string;
  /** 姓名 */
  realName?: string;
  /**
   * 所属交付组ID
   * @format int64
   */
  teamId?: number;
  /** 所属交付组 */
  teamName?: string;
  /** 可用度 */
  workLoad?: string;
  /**
   * 在岗状态
   * @format int64
   */
  workState?: number;
  /** 在岗状态 */
  workStateName?: string;
  /** 月份 */
  yearMonths?: string;
}

/**
 * StaffWorkStatePagedDTO
 * 在职状态
 */
export interface StaffWorkStatePagedDTO {
  /** 排序方式，desc或asc */
  isAsc?: string;
  /** 排序对象，包含排序列和方式，desc或asc */
  items?: OrderItem[];
  /** 工号 */
  jobNumber?: string;
  /** 排序列名(orderByColumn) */
  obc?: string;
  /**
   * 要查询的页号
   * @format int64
   * @example 1
   */
  pageNumber?: number;
  /**
   * 每页包含的数据的条数
   * @format int64
   * @example 10
   */
  pageSize?: number;
  /**
   * 工作状态
   * @format int64
   */
  workState?: number;
  /** 月份 */
  yearMonths?: string;
}

/**
 * StaffWorkStateVO
 * 在职状态
 */
export interface StaffWorkStateVO {
  /**
   * 主键ID
   * @format int64
   */
  id?: number;
  /** 工号 */
  jobNumber?: string;
  /**
   * 可用度
   * @format int32
   */
  workLoad?: number;
  /**
   * 工作状态
   * @format int32
   */
  workState?: number;
  /** 月份 */
  yearMonths?: string;
}

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from 'axios';

import { errorConfig, requestConfig, responseConfig } from '../axiosConfig';

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  'body' | 'method' | 'query' | 'path'
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, 'data' | 'cancelToken'> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: '' });
    this.instance.interceptors.request.use(requestConfig, errorConfig);
    this.instance.interceptors.response.use(responseConfig, errorConfig);
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === 'object' && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === 'object'
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== 'string'
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type && type !== ContentType.FormData
          ? { 'Content-Type': type }
          : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Travelsky Youth Friendship Platform Api Doc
 * @version Application Version：3.0.4-R6-240819032236
 * @baseUrl http://ppgtest.iprd.sw:80
 * @contact swcares team <<EMAIL>>
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  pmt = {
    /**
     * No description
     *
     * @tags 资源岗级接口
     * @name DeleteUsingPost7
     * @summary 通过ID删除资源岗级记录
     * @request POST:/pmt/staff/job_level/delete/{id}
     * @secure
     */
    deleteUsingPost7: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/job_level/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源岗级接口
     * @name GetUsingGet9
     * @summary 通过ID查询资源岗级记录
     * @request GET:/pmt/staff/job_level/get
     * @secure
     */
    getUsingGet9: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfStaffJobLevelVO, void>({
        path: `/pmt/staff/job_level/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源岗级接口
     * @name ImportDataUsingPost
     * @summary 导入数据
     * @request POST:/pmt/staff/job_level/importData
     * @secure
     */
    importDataUsingPost: (
      data: {
        /**
         * file
         * @format binary
         */
        file: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/job_level/importData`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源岗级接口
     * @name PageUsingPost9
     * @summary 条件分页查询资源岗级记录
     * @request POST:/pmt/staff/job_level/page
     * @secure
     */
    pageUsingPost9: (data: StaffJobLevelPagedDTO, params: RequestParams = {}) =>
      this.request<PagedResultOfListOfStaffJobLevelVO, void>({
        path: `/pmt/staff/job_level/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源岗级接口
     * @name SaveUsingPost8
     * @summary 新建资源岗级记录
     * @request POST:/pmt/staff/job_level/save
     * @secure
     */
    saveUsingPost8: (data: StaffJobLevelDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/job_level/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源岗级接口
     * @name UpdateUsingPost8
     * @summary 修改资源岗级记录
     * @request POST:/pmt/staff/job_level/update
     * @secure
     */
    updateUsingPost8: (data: StaffJobLevelDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/job_level/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人力资源接口
     * @name DeleteUsingPost8
     * @summary 通过ID删除人力资源记录
     * @request POST:/pmt/staff/res/delete/{id}
     * @secure
     */
    deleteUsingPost8: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/res/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人力资源接口
     * @name GetUsingGet10
     * @summary 通过ID查询人力资源记录
     * @request GET:/pmt/staff/res/get
     * @secure
     */
    getUsingGet10: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfStaffResources, void>({
        path: `/pmt/staff/res/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人力资源接口
     * @name ImportDataUsingPost1
     * @summary 导入数据
     * @request POST:/pmt/staff/res/importData
     * @secure
     */
    importDataUsingPost1: (
      data: {
        /**
         * file
         * @format binary
         */
        file: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/res/importData`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人力资源接口
     * @name PageUsingPost10
     * @summary 条件分页查询人力资源记录
     * @request POST:/pmt/staff/res/page
     * @secure
     */
    pageUsingPost10: (
      data: StaffResourcesPagedDTO,
      params: RequestParams = {},
    ) =>
      this.request<PagedResultOfListOfStaffResources, void>({
        path: `/pmt/staff/res/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人力资源接口
     * @name SaveUsingPost9
     * @summary 新建人力资源记录
     * @request POST:/pmt/staff/res/save
     * @secure
     */
    saveUsingPost9: (data: StaffResourcesDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/res/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 人力资源接口
     * @name UpdateUsingPost9
     * @summary 修改人力资源记录
     * @request POST:/pmt/staff/res/update
     * @secure
     */
    updateUsingPost9: (data: StaffResourcesDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/res/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源使用记录(负载、投入)接口
     * @name DeleteUsingPost9
     * @summary 通过ID删除资源使用记录记录
     * @request POST:/pmt/staff/wl/delete/{id}
     * @secure
     */
    deleteUsingPost9: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/wl/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源使用记录(负载、投入)接口
     * @name GetUsingGet11
     * @summary 通过ID查询资源使用记录记录
     * @request GET:/pmt/staff/wl/get
     * @secure
     */
    getUsingGet11: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfStaffWorkLoadVO, void>({
        path: `/pmt/staff/wl/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * @description dt：1-负载，2-投入
     *
     * @tags 资源使用记录(负载、投入)接口
     * @name ImportDataUsingPost2
     * @summary 导入数据
     * @request POST:/pmt/staff/wl/importData
     * @secure
     */
    importDataUsingPost2: (
      data: {
        /**
         * file
         * @format binary
         */
        file: File;
      },
      query?: {
        /**
         * dt
         * @format int32
         */
        dt?: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/wl/importData`,
        method: 'POST',
        query: query,
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源使用记录(负载、投入)接口
     * @name PageUsingPost11
     * @summary 条件分页查询资源使用记录记录
     * @request POST:/pmt/staff/wl/page
     * @secure
     */
    pageUsingPost11: (
      data: StaffWorkLoadPagedDTO,
      params: RequestParams = {},
    ) =>
      this.request<PagedResultOfListOfStaffWorkLoadVO, void>({
        path: `/pmt/staff/wl/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源使用记录(负载、投入)接口
     * @name SaveUsingPost10
     * @summary 新建资源使用记录记录
     * @request POST:/pmt/staff/wl/save
     * @secure
     */
    saveUsingPost10: (data: StaffWorkLoadDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/wl/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 资源使用记录(负载、投入)接口
     * @name UpdateUsingPost10
     * @summary 修改资源使用记录记录
     * @request POST:/pmt/staff/wl/update
     * @secure
     */
    updateUsingPost10: (data: StaffWorkLoadDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/wl/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 在岗状态接口
     * @name DeleteUsingPost10
     * @summary 通过ID删除在职状态记录
     * @request POST:/pmt/staff/ws/delete/{id}
     * @secure
     */
    deleteUsingPost10: (id: number, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/ws/delete/${id}`,
        method: 'POST',
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 在岗状态接口
     * @name GetUsingGet12
     * @summary 通过ID查询在职状态记录
     * @request GET:/pmt/staff/ws/get
     * @secure
     */
    getUsingGet12: (
      query: {
        /**
         * 主键id
         * @format int64
         */
        id: number;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfStaffWorkStateVO, void>({
        path: `/pmt/staff/ws/get`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 在岗状态接口
     * @name ImportDataUsingPost3
     * @summary 导入数据
     * @request POST:/pmt/staff/ws/importData
     * @secure
     */
    importDataUsingPost3: (
      data: {
        /**
         * file
         * @format binary
         */
        file: File;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/ws/importData`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 在岗状态接口
     * @name PageUsingPost12
     * @summary 条件分页查询在职状态记录
     * @request POST:/pmt/staff/ws/page
     * @secure
     */
    pageUsingPost12: (
      data: StaffWorkStatePagedDTO,
      params: RequestParams = {},
    ) =>
      this.request<PagedResultOfListOfStaffWorkStateVO, void>({
        path: `/pmt/staff/ws/page`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 在岗状态接口
     * @name SaveUsingPost11
     * @summary 新建在职状态记录
     * @request POST:/pmt/staff/ws/save
     * @secure
     */
    saveUsingPost11: (data: StaffWorkStateDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/ws/save`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 在岗状态接口
     * @name UpdateUsingPost11
     * @summary 修改在职状态记录
     * @request POST:/pmt/staff/ws/update
     * @secure
     */
    updateUsingPost11: (data: StaffWorkStateDTO, params: RequestParams = {}) =>
      this.request<BaseResultOfobject, void>({
        path: `/pmt/staff/ws/update`,
        method: 'POST',
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),
  };
}
