/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

/** BaseResultOfListOfManagementUnitRiskStatVO */
export interface BaseResultOfListOfManagementUnitRiskStatVO {
  /** @format int32 */
  code?: number;
  data?: ManagementUnitRiskStatVO[];
  message?: string;
}

/** BaseResultOfListOfManagementUnitStatVO */
export interface BaseResultOfListOfManagementUnitStatVO {
  /** @format int32 */
  code?: number;
  data?: ManagementUnitStatVO[];
  message?: string;
}

/** BaseResultOfListOfStaffLoadAnalysisVO */
export interface BaseResultOfListOfStaffLoadAnalysisVO {
  /** @format int32 */
  code?: number;
  data?: StaffLoadAnalysisVO[];
  message?: string;
}

/** BaseResultOfMapOfstringAndListOfDictDataCacheVO */
export interface BaseResultOfMapOfstringAndListOfDictDataCacheVO {
  /** @format int32 */
  code?: number;
  data?: Record<string, DictDataCacheVO[]>;
  message?: string;
}

/** BaseResultOfProjectCostPaceStatVO */
export interface BaseResultOfProjectCostPaceStatVO {
  /** @format int32 */
  code?: number;
  /** 大屏项目进度/成本统计对象 */
  data?: ProjectCostPaceStatVO;
  message?: string;
}

/** BaseResultOfProjectLargeScreenVO */
export interface BaseResultOfProjectLargeScreenVO {
  /** @format int32 */
  code?: number;
  /** 大屏项目总览数据对象 */
  data?: ProjectLargeScreenVO;
  message?: string;
}

/** BaseResultOfStaffLargeScreenVO */
export interface BaseResultOfStaffLargeScreenVO {
  /** @format int32 */
  code?: number;
  /** 大屏项目总览数据对象 */
  data?: StaffLargeScreenVO;
  message?: string;
}

/**
 * DeliverWorkLoadStatVO
 * 交付工量统计VO
 */
export interface DeliverWorkLoadStatVO {
  /**
   * 计划编码工量
   * @format bigdecimal
   */
  planCodingWorkLoad?: number;
  /**
   * 计划人工工量
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 统计日期 */
  statDate?: string;
}

/** DictDataCacheVO */
export type DictDataCacheVO = object;

/**
 * ManagementUnitMonthStatVO
 * 经营单元月(进度/成本)统计VO
 */
export interface ManagementUnitMonthStatVO {
  /**
   * 产出量缺口/成本超支
   * @format bigdecimal
   */
  actualFinish?: number;
  /** 经营单元名称 */
  managementName?: string;
  /**
   * 经营单元
   * @format int64
   */
  managementUnit?: number;
  /**
   * 进度符合度/成本利用率
   * @format bigdecimal
   */
  paceRate?: number;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * ManagementUnitRiskStatVO
 * 经营单元进度/成本风险VO
 */
export interface ManagementUnitRiskStatVO {
  /**
   * 成本利用率
   * @format bigdecimal
   */
  costRate?: number;
  /**
   * 成本风险数量
   * @format int32
   */
  costRiskCount?: number;
  /** 经营单元名称 */
  managementName?: string;
  /** 经营单元 */
  managementUnit?: string;
  /**
   * 进度风险数量
   * @format int32
   */
  paceRiskCount?: number;
  /**
   * 产出量缺口
   * @format bigdecimal
   */
  produceGap?: number;
  /**
   * 项目数量
   * @format int32
   */
  projectCount?: number;
  /** 截止月份 */
  statMonth?: string;
}

/**
 * ManagementUnitStatVO
 * 经营单元统计分析VO
 */
export interface ManagementUnitStatVO {
  /** @format int64 */
  managementUnit?: number;
  managementUnitName?: string;
  stats?: ManagementUnitMonthStatVO[];
}

/**
 * ProjectAnalysisStatVO
 * 项目分析统计VO
 */
export interface ProjectAnalysisStatVO {
  /**
   * 完成编码工量
   * @format bigdecimal
   */
  finishCodingWorkLoad?: number;
  /**
   * 计划编码工量
   * @format bigdecimal
   */
  planCodingWorkLoad?: number;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * ProjectCostPaceStatVO
 * 大屏项目进度/成本统计对象
 */
export interface ProjectCostPaceStatVO {
  /** 成本统计数据 */
  costs?: ProjectAnalysisStatVO[];
  /** 进度统计数据 */
  paces?: ProjectAnalysisStatVO[];
}

/**
 * ProjectLargeScreenVO
 * 大屏项目总览数据对象
 */
export interface ProjectLargeScreenVO {
  /** 交付工量 */
  delivers?: DeliverWorkLoadStatVO[];
  /** 项目总览 */
  overview?: ProjectOverviewVO;
  /** 项目分级 */
  priority?: ProjectPriorityStatVO;
  /** 项目阶段 */
  stage?: ProjectStageStatVO;
}

/**
 * ProjectOverviewVO
 * 项目总览
 */
export interface ProjectOverviewVO {
  /**
   * 预计编码人工成本
   * @format bigdecimal
   */
  codingWorkCost?: number;
  /**
   * 预计人工成本
   * @format bigdecimal
   */
  planWorkLoad?: number;
  /** 统计年份 */
  statYear?: string;
  /**
   * 项目总数量
   * @format int32
   */
  totalQuantity?: number;
}

/**
 * ProjectPriorityStatVO
 * 项目优先级统计VO
 */
export interface ProjectPriorityStatVO {
  /**
   * 四级
   * @format int32
   */
  fourLevel?: number;
  /**
   * 一级
   * @format int32
   */
  oneLevel?: number;
  /**
   * 三级
   * @format int32
   */
  threeLevel?: number;
  /**
   * 项目总数量
   * @format int32
   */
  totalQuantity?: number;
  /**
   * 二级
   * @format int32
   */
  twoLevel?: number;
}

/**
 * ProjectStageStatVO
 * 项目阶段统计VO
 */
export interface ProjectStageStatVO {
  /**
   * 验收数量
   * @format int32
   */
  acceptance?: number;
  /**
   * 已结项数量
   * @format int32
   */
  completed?: number;
  /**
   * 预立项数量
   * @format int32
   */
  expectApproval?: number;
  /**
   * 结项数量
   * @format int32
   */
  knot?: number;
  /**
   * 运维数量
   * @format int32
   */
  maintenance?: number;
  /**
   * 售前数量
   * @format int32
   */
  preSales?: number;
  /**
   * 执行数量
   * @format int32
   */
  pursue?: number;
  /**
   * 项目总数量
   * @format int32
   */
  totalQuantity?: number;
}

/**
 * StaffJobStatVO
 * 资源岗级统计VO
 */
export interface StaffJobStatVO {
  /**
   * 岗位ID
   * @format int64
   */
  jobId?: number;
  /** 岗位名称 */
  jobName?: string;
  /**
   * 中级数量
   * @format int32
   */
  middleCount?: number;
  /**
   * 初级数量
   * @format int32
   */
  primaryCount?: number;
  /**
   * 高级数量
   * @format int32
   */
  seniorCount?: number;
}

/**
 * StaffLargeScreenVO
 * 大屏项目总览数据对象
 */
export interface StaffLargeScreenVO {
  /** 资源总览 */
  resOverview?: StaffResOverviewVO;
  /** 在岗情况 */
  workStates?: StaffWorkStateStatVO[];
}

/**
 * StaffLoadAnalysisVO
 * 资源负载分析VO
 */
export interface StaffLoadAnalysisVO {
  /**
   * 平均负载度
   * @format bigdecimal
   */
  avgLoad?: number;
  /**
   * 最大负载度
   * @format bigdecimal
   */
  maxLoad?: number;
  /**
   * 资源利用率
   * @format bigdecimal
   */
  monthUsage?: number;
  /** 统计月份 */
  statMonth?: string;
}

/**
 * StaffResOverviewVO
 * 资源总览统计VO
 */
export interface StaffResOverviewVO {
  /** 岗级对比 */
  jobs?: StaffJobStatVO[];
  /**
   * 年度可用人员
   * @format int32
   */
  yearStaffTotal?: number;
  /**
   * 年度可用工量
   * @format int32
   */
  yearWorkLoad?: number;
}

/**
 * StaffWorkStateStatVO
 * 资源在岗统计VO
 */
export interface StaffWorkStateStatVO {
  /**
   * 在岗人数
   * @format int32
   */
  dutyNumber?: number;
  /**
   * 不在岗人数
   * @format int32
   */
  notDutyNumber?: number;
  /** 统计月份 */
  statMonth?: string;
}

import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from 'axios';

import { errorConfig, requestConfig, responseConfig } from '../axiosConfig';

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<
  FullRequestParams,
  'body' | 'method' | 'query' | 'path'
>;

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, 'data' | 'cancelToken'> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker'];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: '' });
    this.instance.interceptors.request.use(requestConfig, errorConfig);
    this.instance.interceptors.response.use(responseConfig, errorConfig);
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[
            method.toLowerCase() as keyof HeadersDefaults
          ]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === 'object' && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] =
        property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(
          key,
          isFileType ? formItem : this.stringifyFormItem(formItem),
        );
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (
      type === ContentType.FormData &&
      body &&
      body !== null &&
      typeof body === 'object'
    ) {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (
      type === ContentType.Text &&
      body &&
      body !== null &&
      typeof body !== 'string'
    ) {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type && type !== ContentType.FormData
          ? { 'Content-Type': type }
          : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Travelsky Youth Friendship Platform Api Doc
 * @version Application Version：3.0.4-R6-240902070838
 * @baseUrl http://ppgtest.iprd.sw:80
 * @contact swcares team <<EMAIL>>
 */
export class Api<
  SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
  pmt = {
    /**
     * No description
     *
     * @tags 大屏接口
     * @name MgrUnitCostUsingGet
     * @summary 经营单元成本利用率
     * @request GET:/pmt/pls/mut_cost
     * @secure
     */
    mgrUnitCostUsingGet: (
      query?: {
        /** 所属年度(2024) */
        projectYear?: string;
        /** 统计截止月份(2024-08) */
        statMonth?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfManagementUnitStatVO, void>({
        path: `/pmt/pls/mut_cost`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 大屏接口
     * @name MgrUnitPaceUsingGet
     * @summary 经营单元进度符合度
     * @request GET:/pmt/pls/mut_pace
     * @secure
     */
    mgrUnitPaceUsingGet: (
      query?: {
        /** 所属年度(2024) */
        projectYear?: string;
        /** 统计截止月份(2024-08) */
        statMonth?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfManagementUnitStatVO, void>({
        path: `/pmt/pls/mut_pace`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 大屏接口
     * @name MgrUnitRiskUsingGet
     * @summary 经营单元成本/进度风险
     * @request GET:/pmt/pls/mut_risk
     * @secure
     */
    mgrUnitRiskUsingGet: (
      query?: {
        /** 所属年度(2024) */
        projectYear?: string;
        /** 统计截止月份(2024-08) */
        statMonth?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfManagementUnitRiskStatVO, void>({
        path: `/pmt/pls/mut_risk`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 大屏接口
     * @name ProjectPaceCostStatUsingGet
     * @summary 项目进度/成本统计
     * @request GET:/pmt/pls/pcs
     * @secure
     */
    projectPaceCostStatUsingGet: (
      query?: {
        /** 所属年度(2024) */
        projectYear?: string;
        /** 统计截止月份(2024-08) */
        statMonth?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectCostPaceStatVO, void>({
        path: `/pmt/pls/pcs`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 大屏接口
     * @name ProjectOverviewUsingGet
     * @summary 项目总览
     * @request GET:/pmt/pls/pow
     * @secure
     */
    projectOverviewUsingGet: (
      query?: {
        /**
         * 成本风险
         * @format int32
         */
        costRisk?: number;
        /** 客户单位 */
        customerName?: string;
        /** 项目经理工号 */
        jobNumber?: string;
        /**
         * 所属经营单元
         * @format int64
         */
        managementUnit?: number;
        /**
         * 进度风险
         * @format int32
         */
        paceRisk?: number;
        /**
         * 优先级
         * @format int32
         */
        priorityLevel?: number;
        /** 项目简称 */
        projectAlias?: string;
        /** 项目编号 */
        projectCode?: string;
        /** 项目经理 */
        projectManager?: string;
        /** 项目名称 */
        projectName?: string;
        /**
         * 项目性质
         * @format int32
         */
        projectNature?: number;
        /** 项目编码 */
        projectSn?: string;
        /**
         * 项目阶段
         * @format int32
         */
        projectStage?: number;
        /** 所属年度 */
        projectYear?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfProjectLargeScreenVO, void>({
        path: `/pmt/pls/pow`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 大屏接口
     * @name StaffLoadUsingGet
     * @summary 资源负载分析，包含负载度、利用率数据
     * @request GET:/pmt/pls/staff_load
     * @secure
     */
    staffLoadUsingGet: (
      query?: {
        /** 所属年度(2024) */
        projectYear?: string;
        /** 统计截止月份(2024-08) */
        statMonth?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfListOfStaffLoadAnalysisVO, void>({
        path: `/pmt/pls/staff_load`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 大屏接口
     * @name StaffOverviewUsingGet
     * @summary 资源总览
     * @request GET:/pmt/pls/staff_ow
     * @secure
     */
    staffOverviewUsingGet: (
      query?: {
        /** 所属年度(2024) */
        projectYear?: string;
        /** 统计截止月份(2024-08) */
        statMonth?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<BaseResultOfStaffLargeScreenVO, void>({
        path: `/pmt/pls/staff_ow`,
        method: 'GET',
        query: query,
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags 数据字典接口
     * @name GetAllUsingGet2
     * @summary 加载全部数据字典
     * @request GET:/pmt/pls/dist/get_all
     * @secure
     */
    getAllUsingGet2: (params: RequestParams = {}) =>
      this.request<BaseResultOfMapOfstringAndListOfDictDataCacheVO, void>({
        path: `/pmt/pls/dist/get_all`,
        method: 'GET',
        secure: true,
        ...params,
      }),
  };
}
