import { create } from 'zustand';

type Store = {
  tableParams: { [key: string]: any };
  updateTableParams: (storeKey: string | undefined, params: any) => void;
};

const useStore = create<Store>()((set) => ({
  tableParams: {},
  updateTableParams: (storeKey, params) => {
    // console.log('🚀🚀🚀 ~ params:', storeKey, params);

    if (!storeKey) {
      return;
    }

    return set((store) => {
      // console.log('🚀🚀🚀 ~ store:', store);

      return {
        tableParams: {
          ...store.tableParams,
          [storeKey]: params,
        },
      };
    });
  },
}));

export default useStore;
