module.exports = {
  // 可选类型
  types: [
    { value: 'init', name: 'init:     开始一个新项目' },
    { value: 'feat', name: 'feat:     添加新功能' },
    { value: 'fix', name: 'fix:      修复bug' },
    { value: 'docs', name: 'docs:     文档变更' },
    {
      value: 'style',
      name: 'style:    代码风格，不影响代码意义',
    },
    {
      value: 'refactor',
      name: 'refactor: 代码重构，既不修复bug也不添加新功能',
    },
    {
      value: 'perf',
      name: 'perf:     提升性能',
    },
    { value: 'test', name: 'test:     添加缺失测试' },
    {
      value: 'chore',
      name: 'chore:    更改构建过程或辅助工具和库等',
    },
    { value: 'revert', name: 'revert:   回滚到之前的提交' },
    { value: 'WIP', name: 'WIP:      工作进行中' },
  ],

  // 覆盖消息
  messages: {
    type: '请选择你的提交类型:',
    scope: '\n指明这次改变的影响范围 (可选):',
    // 用于询问变更摘要的消息
    customScope: '指明这次改变的影响范围:',
    description: '提供一个简短的变更描述:',
    body: '提供更详细的变更描述 (可选)。使用 "|" 来换行:',
    breaking: '列出任何破坏性变更 (可选):',
    footer: '列出这次变更关闭的任何问题 (可选)。例如: #31, #34:',
    confirmCommit: '你确定要提交以上的变更吗?',
  },

  // 是否允许自定义范围
  allowCustomScopes: true,

  // 是否允许破坏性更改
  allowBreakingChanges: ['feat', 'fix'],

  // 跳过问题
  skipQuestions: ['body', 'footer'],

  // 限制标题长度
  subjectLimit: 100,

  // 破坏性更改前缀
  breaklineChar: '|', // 用于在body和footer中输入换行符的字符

  // 尾部标注关闭的issue
  footerPrefix: '关闭问题:',

  // 是否将提交类型转换为小写
  lowerCaseType: false,
};
